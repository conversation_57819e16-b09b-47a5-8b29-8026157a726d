"""
SBCDP 异步方法实现 - 重构版
使用组合模式 + 代理模式拆分原来臃肿的AsyncCDPMethods类
"""

from typing import Optional


class AsyncCDPMethods:
    """异步CDP方法类 - 使用组合模式重构"""

    def __init__(self, page, driver):
        self.page = page
        self.driver = driver

        # 使用组合模式集成各个API模块
        from ..api.navigation import NavigationMethods
        from ..api.element import ElementMethods
        from ..api.action import ActionMethods
        from ..api.wait import WaitMethods
        from ..api.page_info import PageInfoMethods
        from ..api.gui import GUIMethods

        self._navigation = NavigationMethods(page, driver)
        self._element = ElementMethods(page, driver)
        self._action = ActionMethods(page, driver)
        self._wait = WaitMethods(page, driver)
        self._page_info = PageInfoMethods(page, driver)
        self._gui = GUIMethods(page, driver)

    # === 导航方法代理 ===
    async def get(self, url, new_tab=False, new_window=False, **kwargs):
        """导航到指定URL"""
        return await self._navigation.get(url, new_tab=new_tab, new_window=new_window, **kwargs)

    async def open(self, url, new_tab=False, new_window=False, **kwargs):
        """打开指定URL（get方法的别名）"""
        return await self._navigation.open(url, new_tab=new_tab, new_window=new_window, **kwargs)

    async def reload(self, ignore_cache=True, script_to_evaluate_on_load=None):
        """重新加载页面"""
        return await self._navigation.reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load)

    async def refresh(self, *args, **kwargs):
        """刷新页面（reload方法的别名）"""
        return await self._navigation.refresh(*args, **kwargs)

    async def go_back(self):
        """后退"""
        return await self._navigation.go_back()

    async def go_forward(self):
        """前进"""
        return await self._navigation.go_forward()

    async def get_navigation_history(self):
        """获取导航历史"""
        return await self._navigation.get_navigation_history()

    async def open_new_tab(self, url=None, switch_to=True):
        """打开新标签页"""
        return await self._navigation.open_new_tab(url=url, switch_to=switch_to)

    async def open_new_window(self, url=None, switch_to=True):
        """打开新窗口"""
        return await self._navigation.open_new_window(url=url, switch_to=switch_to)

    async def switch_to_tab(self, tab):
        """切换到指定标签页"""
        return await self._navigation.switch_to_tab(tab)

    async def switch_to_newest_tab(self):
        """切换到最新的标签页"""
        return await self._navigation.switch_to_newest_tab()

    async def switch_to_window(self, window):
        """切换到指定窗口"""
        return await self._navigation.switch_to_window(window)

    async def switch_to_newest_window(self):
        """切换到最新的窗口"""
        return await self._navigation.switch_to_newest_window()

    async def close_active_tab(self):
        """关闭当前活动的标签页"""
        return await self._navigation.close_active_tab()

    async def get_active_tab(self):
        """获取当前活动的标签页"""
        return await self._navigation.get_active_tab()

    async def get_tabs(self):
        """获取所有标签页"""
        return await self._navigation.get_tabs()

    async def get_window(self):
        """获取窗口信息"""
        return await self._navigation.get_window()

    async def bring_active_window_to_front(self):
        """将活动窗口置于前台"""
        return await self._navigation.bring_active_window_to_front()

    async def tile_windows(self, windows=None, max_columns=0):
        """平铺窗口"""
        return await self._navigation.tile_windows(windows=windows, max_columns=max_columns)

    async def grant_permissions(self, permissions, origin=None):
        """授予权限"""
        return await self._navigation.grant_permissions(permissions, origin=origin)

    async def grant_all_permissions(self):
        """授予所有权限"""
        return await self._navigation.grant_all_permissions()

    async def add_handler(self, event, handler):
        """添加事件处理器"""
        return await self._navigation.add_handler(event, handler)

    # === 元素方法代理 ===
    async def find_element(self, selector, best_match=False, timeout=None):
        """查找单个元素"""
        return await self._element.find_element(selector, best_match=best_match, timeout=timeout)

    async def find_element_by_text(self, text, tag_name=None, timeout=None):
        """通过文本内容查找元素"""
        return await self._element.find_element_by_text(text, tag_name=tag_name, timeout=timeout)

    async def find_elements_by_text(self, text, tag_name=None, timeout=None):
        """通过文本内容查找多个元素"""
        return await self._element.find_elements_by_text(text, tag_name=tag_name, timeout=timeout)

    async def find_all(self, selector, timeout=None):
        """查找所有匹配的元素"""
        return await self._element.find_all(selector, timeout=timeout)

    async def select(self, selector, timeout=None):
        """选择单个元素"""
        return await self._element.select(selector, timeout=timeout)

    async def select_all(self, selector, timeout=None):
        """选择所有匹配的元素"""
        return await self._element.select_all(selector, timeout=timeout)

    async def find_elements(self, selector, timeout=None):
        """查找多个元素"""
        return await self._element.find_elements(selector, timeout=timeout)

    async def find_visible_elements(self, selector, timeout=None):
        """查找所有可见的元素"""
        return await self._element.find_visible_elements(selector, timeout=timeout)

    async def click(self, selector, timeout=None):
        """点击元素"""
        return await self._element.click(selector, timeout=timeout)

    async def click_nth_element(self, selector, number):
        """点击第N个匹配的元素"""
        return await self._element.click_nth_element(selector, number)

    async def click_nth_visible_element(self, selector, number):
        """点击第N个可见的元素"""
        return await self._element.click_nth_visible_element(selector, number)

    async def click_link(self, link_text):
        """点击链接文本"""
        return await self._element.click_link(link_text)

    async def mouse_click(self, selector, timeout=None):
        """模拟鼠标点击"""
        return await self._element.mouse_click(selector, timeout=timeout)

    async def nested_click(self, parent_selector, selector):
        """在父元素内点击子元素"""
        return await self._element.nested_click(parent_selector, selector)

    async def get_nested_element(self, parent_selector, selector):
        """获取父元素内的子元素"""
        return await self._element.get_nested_element(parent_selector, selector)

    async def get_active_element(self):
        """获取当前活动的元素"""
        return await self._element.get_active_element()

    async def get_active_element_css(self):
        """获取当前活动元素的CSS选择器"""
        return await self._element.get_active_element_css()

    # === 动作方法代理 ===
    async def send_keys(self, selector, text, timeout=None):
        """向元素发送按键"""
        return await self._action.send_keys(selector, text, timeout=timeout)

    async def press_keys(self, selector, text, timeout=None):
        """以人类速度按键"""
        return await self._action.press_keys(selector, text, timeout=timeout)

    async def type(self, selector, text, timeout=None):
        """输入文本（先清空字段）"""
        return await self._action.type(selector, text, timeout=timeout)

    async def set_value(self, selector, text, timeout=None):
        """设置元素值"""
        return await self._action.set_value(selector, text, timeout=timeout)

    async def clear(self, selector, timeout=None):
        """清空输入字段"""
        return await self._action.clear(selector, timeout=timeout)

    async def submit(self, selector="form"):
        """提交表单"""
        return await self._action.submit(selector)

    async def get_text(self, selector):
        """获取元素文本"""
        return await self._action.get_text(selector)

    async def get_attribute(self, selector, attribute):
        """获取元素属性值"""
        return await self._action.get_attribute(selector, attribute)

    async def get_element_attribute(self, selector, attribute):
        """获取元素属性值（带异常处理）"""
        return await self._action.get_element_attribute(selector, attribute)

    async def get_element_attributes(self, selector):
        """获取元素所有属性"""
        return await self._action.get_element_attributes(selector)

    async def get_element_html(self, selector):
        """获取元素HTML"""
        return await self._action.get_element_html(selector)

    async def set_attribute(self, selector, attribute, value):
        """设置元素属性"""
        return await self._action.set_attribute(selector, attribute, value)

    async def set_attributes(self, selector, attribute, value):
        """为所有匹配元素设置属性"""
        return await self._action.set_attributes(selector, attribute, value)

    async def remove_attribute(self, selector, attribute):
        """移除元素属性"""
        return await self._action.remove_attribute(selector, attribute)

    async def remove_attributes(self, selector, attribute):
        """移除所有匹配元素的属性"""
        return await self._action.remove_attributes(selector, attribute)

    async def remove_elements(self, selector):
        """移除所有匹配的元素"""
        return await self._action.remove_elements(selector)

    async def set_locale(self, locale):
        """设置语言环境"""
        return await self._action.set_locale(locale)

    async def internalize_links(self):
        """将所有target="_blank"链接改为target="_self" """
        return await self._action.internalize_links()

    # === 等待方法代理 ===
    async def is_element_present(self, selector):
        """检查元素是否存在"""
        return await self._wait.is_element_present(selector)

    async def is_element_visible(self, selector):
        """检查元素是否可见"""
        return await self._wait.is_element_visible(selector)

    async def is_text_visible(self, text, selector="body"):
        """检查文本是否可见"""
        return await self._wait.is_text_visible(text, selector=selector)

    async def is_exact_text_visible(self, text, selector="body"):
        """检查精确文本是否可见"""
        return await self._wait.is_exact_text_visible(text, selector=selector)

    async def wait_for_element_visible(self, selector, timeout=None):
        """等待元素可见"""
        return await self._wait.wait_for_element_visible(selector, timeout=timeout)

    async def wait_for_element_not_visible(self, selector, timeout=None):
        """等待元素不可见"""
        return await self._wait.wait_for_element_not_visible(selector, timeout=timeout)

    async def wait_for_element_absent(self, selector, timeout=None):
        """等待元素从DOM中消失"""
        return await self._wait.wait_for_element_absent(selector, timeout=timeout)

    async def wait_for_text(self, text, selector="body", timeout=None):
        """等待文本出现"""
        return await self._wait.wait_for_text(text, selector=selector, timeout=timeout)

    async def wait_for_text_not_visible(self, text, selector="body", timeout=None):
        """等待文本不可见"""
        return await self._wait.wait_for_text_not_visible(text, selector=selector, timeout=timeout)

    async def assert_element(self, selector, timeout=None):
        """断言元素存在且可见"""
        return await self._wait.assert_element(selector, timeout=timeout)

    async def assert_element_visible(self, selector, timeout=None):
        """断言元素可见"""
        return await self._wait.assert_element_visible(selector, timeout=timeout)

    async def assert_text(self, text, selector="body", timeout=None):
        """断言文本存在"""
        return await self._wait.assert_text(text, selector=selector, timeout=timeout)

    async def assert_exact_text(self, text, selector="body", timeout=None):
        """断言精确文本存在"""
        return await self._wait.assert_exact_text(text, selector=selector, timeout=timeout)

    async def sleep(self, seconds):
        """等待指定秒数"""
        return await self._wait.sleep(seconds)

    async def wait_for(self, selector="", text="", timeout=10):
        """通用等待方法"""
        return await self._wait.wait_for(selector=selector, text=text, timeout=timeout)

    # === 页面信息方法代理 ===
    async def get_title(self):
        """获取页面标题"""
        return await self._page_info.get_title()

    async def get_current_url(self):
        """获取当前URL"""
        return await self._page_info.get_current_url()

    async def get_origin(self):
        """获取页面来源"""
        return await self._page_info.get_origin()

    async def get_page_source(self):
        """获取页面源码"""
        return await self._page_info.get_page_source()

    async def get_user_agent(self):
        """获取用户代理"""
        return await self._page_info.get_user_agent()

    async def get_cookie_string(self):
        """获取Cookie字符串"""
        return await self._page_info.get_cookie_string()

    async def get_locale_code(self):
        """获取语言代码"""
        return await self._page_info.get_locale_code()

    async def get_local_storage_item(self, key):
        """获取localStorage项"""
        return await self._page_info.get_local_storage_item(key)

    async def get_session_storage_item(self, key):
        """获取sessionStorage项"""
        return await self._page_info.get_session_storage_item(key)

    async def get_screen_rect(self):
        """获取屏幕矩形信息"""
        return await self._page_info.get_screen_rect()

    async def get_element_rect(self, selector, timeout=None):
        """获取元素矩形信息"""
        return await self._page_info.get_element_rect(selector, timeout=timeout)

    async def get_gui_element_rect(self, selector, timeout=None):
        """获取GUI元素矩形信息"""
        return await self._page_info.get_gui_element_rect(selector, timeout=timeout)

    async def get_gui_element_center(self, selector, timeout=None):
        """获取GUI元素中心点坐标"""
        return await self._page_info.get_gui_element_center(selector, timeout=timeout)

    async def get_window_rect(self):
        """获取窗口矩形信息"""
        return await self._page_info.get_window_rect()

    async def get_document(self):
        """获取文档对象"""
        return await self._page_info.get_document()

    async def get_flattened_document(self):
        """获取扁平化文档对象"""
        return await self._page_info.get_flattened_document()

    async def evaluate(self, expression):
        """执行JavaScript表达式"""
        return await self._page_info.evaluate(expression)

    async def execute_script(self, script, *args):
        """执行JavaScript脚本"""
        return await self._page_info.execute_script(script, *args)

    async def save_screenshot(self, name, folder=None, selector=None):
        """保存截图"""
        return await self._page_info.save_screenshot(name, folder=folder, selector=selector)

    async def print_to_pdf(self, name, folder=None):
        """打印为PDF"""
        return await self._page_info.print_to_pdf(name, folder=folder)

    async def scroll_into_view(self, selector):
        """滚动元素到可视区域"""
        return await self._page_info.scroll_into_view(selector)

    async def scroll_to_y(self, y):
        """滚动到指定Y坐标"""
        return await self._page_info.scroll_to_y(y)

    async def scroll_to_top(self):
        """滚动到页面顶部"""
        return await self._page_info.scroll_to_top()

    async def scroll_to_bottom(self):
        """滚动到页面底部"""
        return await self._page_info.scroll_to_bottom()

    async def scroll_up(self, amount=25):
        """向上滚动"""
        return await self._page_info.scroll_up(amount=amount)

    async def scroll_down(self, amount=25):
        """向下滚动"""
        return await self._page_info.scroll_down(amount=amount)

    async def set_local_storage_item(self, key, value):
        """设置localStorage项"""
        return await self._page_info.set_local_storage_item(key, value)

    async def set_session_storage_item(self, key, value):
        """设置sessionStorage项"""
        return await self._page_info.set_session_storage_item(key, value)

    # === GUI方法代理 ===
    async def gui_press_key(self, key):
        """GUI按键"""
        return await self._gui.gui_press_key(key)

    async def gui_press_keys(self, keys):
        """GUI按多个键"""
        return await self._gui.gui_press_keys(keys)

    async def gui_write(self, text):
        """GUI写入文本"""
        return await self._gui.gui_write(text)

    async def gui_click_x_y(self, x, y, timeframe=0.25):
        """GUI点击坐标"""
        return await self._gui.gui_click_x_y(x, y, timeframe=timeframe)

    async def gui_click_element(self, selector, timeframe=0.25):
        """GUI点击元素"""
        return await self._gui.gui_click_element(selector, timeframe=timeframe)

    async def gui_drag_drop_points(self, x1, y1, x2, y2, timeframe=0.35):
        """GUI拖拽坐标点"""
        return await self._gui.gui_drag_drop_points(x1, y1, x2, y2, timeframe=timeframe)

    async def gui_drag_and_drop(self, drag_selector, drop_selector, timeframe=0.35):
        """GUI拖拽元素"""
        return await self._gui.gui_drag_and_drop(drag_selector, drop_selector, timeframe=timeframe)

    async def gui_click_and_hold(self, selector, timeframe=0.35):
        """GUI点击并保持"""
        return await self._gui.gui_click_and_hold(selector, timeframe=timeframe)

    async def gui_hover_x_y(self, x, y, timeframe=0.25):
        """GUI悬停坐标"""
        return await self._gui.gui_hover_x_y(x, y, timeframe=timeframe)

    async def gui_hover_element(self, selector, timeframe=0.25):
        """GUI悬停元素"""
        return await self._gui.gui_hover_element(selector, timeframe=timeframe)

    async def gui_hover_and_click(self, hover_selector, click_selector):
        """GUI悬停并点击"""
        return await self._gui.gui_hover_and_click(hover_selector, click_selector)
