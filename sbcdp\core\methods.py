"""
SBCDP 异步方法实现
纯异步的CDP操作方法
"""

import asyncio

# 导入拆分后的方法类
from .methods_base import AsyncCDPBase
from .methods_element import AsyncElementMethods
from .methods_navigation import AsyncNavigationMethods
from .methods_gui import AsyncGUIMethods
from .methods_page_info import AsyncPageInfoMethods
from .methods_wait import AsyncWaitMethods
from .methods_action import AsyncActionMethods


class AsyncCDPMethods(
    AsyncCDPBase,
    AsyncElementMethods,
    AsyncNavigationMethods,
    AsyncGUIMethods,
    AsyncPageInfoMethods,
    AsyncWaitMethods,
    AsyncActionMethods,
):
    """异步CDP方法类 - 通过多重继承组合各个功能模块"""

    def __init__(self, page, driver):
        # 调用基础类的初始化方法
        super().__init__(page, driver)


class SyncCDPMethods(AsyncCDPMethods):
    """同步CDP方法类 - 包装异步方法"""

    def __init__(self, loop, page, driver):
        self.loop = loop
        self.doing = False
        super(SyncCDPMethods, self).__init__(page, driver)

    def __getattribute__(self, name):
        """动态代理所有方法到异步版本"""
        attr = super().__getattribute__(name)
        if asyncio.iscoroutinefunction(attr):
            # 如果是协程函数，包装为同步调用
            def sync_wrapper(*args, **kwargs):
                return self._run_sync(attr(*args, **kwargs))
            return sync_wrapper
        return attr

    def _run_sync(self, coro):
        """同步执行协程"""
        if not self.doing:
            try:
                self.doing = True
                return self.loop.run_until_complete(coro)
            finally:
                self.doing = False
        return coro