"""
SBCDP 异步方法实现 - 重构版
使用组合模式 + 代理模式拆分原来臃肿的AsyncCDPMethods类
"""

import asyncio
import sys
from contextlib import suppress

# 使用组合模式集成各个API模块
from ..api.navigation import Navigation
from ..api.element import Element
from ..api.action import Action
from ..api.wait import Wait
from ..api.page_info import PageInfo
from ..api.gui import GUI

from ..fixtures import page_utils, js_utils


class Base:
    def __init__(self, page, driver):
        self.page = page
        self.driver = driver

    async def _slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        if (
            (hasattr(self.driver.config, "demo_mode") and self.driver.config.demo_mode)
            or "--demo" in sys.argv
        ):
            await asyncio.sleep(0.48)
        elif (
            (hasattr(self.driver.config, "slow_mode") and self.driver.config.slow_mode)
            or "--slow" in sys.argv
        ):
            await asyncio.sleep(0.24)

    async def _add_light_pause(self):
        """添加轻微暂停"""
        await asyncio.sleep(0.007)

    def _convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        if page_utils.is_xpath_selector(selector):
            with suppress(Exception):
                css = js_utils.convert_to_css_selector(selector, "xpath")
                if css:
                    return css
        return selector
    
    def _add_element_methods(self, element):
        if not element:
            return element
        element.clear_input = lambda: self._clear_input(element)
        element.click = lambda: self._click(element)
        element.flash = (lambda *args, **kwargs: self._flash(element, *args, **kwargs))
        element.focus = lambda: self._focus(element)
        element.gui_click = (lambda *args, **kwargs: self._gui_click(element, *args, **kwargs))
        element.highlight_overlay = lambda: self._highlight_overlay(element)
        element.mouse_click = lambda: self._mouse_click(element)
        element.mouse_drag = lambda destination: self._mouse_drag(element, destination)
        element.mouse_move = lambda: self._mouse_move(element)
        element.press_keys = lambda text: self._press_keys(element, text)
        element.query_selector = lambda selector: self._query_selector(element, selector)
        element.querySelector = element.query_selector
        element.query_selector_all = lambda selector: self._query_selector_all(element, selector)
        element.querySelectorAll = element.query_selector_all
        element.remove_from_dom = lambda: self._remove_from_dom(element)
        element.save_screenshot = (lambda *args, **kwargs: self._save_screenshot(element, *args, **kwargs))
        element.save_to_dom = lambda: self._save_to_dom(element)
        element.scroll_into_view = lambda: self._scroll_into_view(element)
        element.select_option = lambda: self._select_option(element)
        element.send_file = lambda *file_paths: self._send_file(element, *file_paths)
        element.send_keys = lambda text: self._send_keys(element, text)
        element.set_text = lambda value: self._set_text(element, value)
        element.set_value = lambda value: self._set_value(element, value)
        element.type = lambda text: self._type(element, text)
        element.get_position = lambda: self._get_position(element)
        element.get_html = lambda: self._get_html(element)
        element.get_js_attributes = lambda: self._get_js_attributes(element)
        element.get_attribute = lambda attribute: self._get_attribute(element, attribute)
        # element.get_parent() should come last
        element.get_parent = lambda: self._get_parent(element)

        # shadow root
        element.sr_query_selector = (lambda selector: self._shadow_root_query_selector(element, selector))
        element.shadow_root_query_selector = element.sr_query_selector
        element.sr_query_selector_all = (lambda selector: self._shadow_root_query_selector_all(element, selector))
        element.shadow_root_query_selector_all = element.sr_query_selector_all
        return element

    async def _clear_input(self, element):
        return await element.clear_input_async()

    async def _shadow_root_query_selector(self, element, selector: str):
        selector = self._convert_to_css_if_xpath(selector)
        element2 = await element.shadow_root_query_selector_async(selector)
        element2 = self._add_element_methods(element2)
        return element2

    async def _shadow_root_query_selector_all(self, element, selector):
        selector = self._convert_to_css_if_xpath(selector)
        elements = await element.shadow_root_query_selector_all_async(selector)
        updated_elements = []
        for element in elements:
            element = self._add_element_methods(element)
            updated_elements.append(element)
        await self._slow_mode_pause_if_set()
        return updated_elements

    async def _click(self, element):
        result = await element.click_async()
        await self.page.wait()
        return result

    async def _flash(self, element, *args, **kwargs):
        await element.scroll_into_view()
        if len(args) < 3 and "x_offset" not in kwargs:
            x_offset = await self._get_x_scroll_offset()
            kwargs["x_offset"] = x_offset
        if len(args) < 3 and "y_offset" not in kwargs:
            y_offset = await self._get_y_scroll_offset()
            kwargs["y_offset"] = y_offset
        return await element.flash_async(*args, **kwargs)

    async def _focus(self, element):
        return await element.focus_async()

    async def _gui_click(self, element, timeframe=None):
        element.scroll_into_view()
        await self._add_light_pause()
        position = element.get_position()
        x = position.x
        y = position.y
        e_width = position.width
        e_height = position.height
        # Relative to window
        element_rect = {"height": e_height, "width": e_width, "x": x, "y": y}
        window_rect = await self.get_window_rect()
        w_bottom_y = window_rect["y"] + window_rect["height"]
        viewport_height = window_rect["innerHeight"]
        x = window_rect["x"] + element_rect["x"]
        y = w_bottom_y - viewport_height + element_rect["y"]
        y_scroll_offset = window_rect["pageYOffset"]
        y = y - y_scroll_offset
        x = x + window_rect["scrollX"]
        y = y + window_rect["scrollY"]
        # Relative to screen
        element_rect = {"height": e_height, "width": e_width, "x": x, "y": y}
        e_width = element_rect["width"]
        e_height = element_rect["height"]
        e_x = element_rect["x"]
        e_y = element_rect["y"]
        x, y = ((e_x + e_width / 2.0) + 0.5), ((e_y + e_height / 2.0) + 0.5)
        if not timeframe or not isinstance(timeframe, (int, float)):
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3
        await self.gui_click_x_y(x, y, timeframe=timeframe)
        return await self.page.wait()

    async def _highlight_overlay(self, element):
        return await element.highlight_overlay_async()

    async def _mouse_click(self, element):
        result = await element.mouse_click_async()
        await self.page.wait()
        return result

    async def _mouse_drag(self, element, destination):
        return await element.mouse_drag_async(destination)

    async def _mouse_move(self, element):
        return await element.mouse_move_async()

    async def _press_keys(self, element, text):
        await element.scroll_into_view()
        submit = False
        if text.endswith("\n") or text.endswith("\r"):
            submit = True
            text = text[:-1]
        for key in text:
            await element.send_keys(key)
            await asyncio.sleep(0.044)
        if submit:
            await element.send_keys("\r\n")
            await asyncio.sleep(0.044)
        await self._slow_mode_pause_if_set()
        return await self.page.sleep(0.025)

    async def _query_selector(self, element, selector):
        selector = self._convert_to_css_if_xpath(selector)
        element2 = await element.query_selector_async(selector)
        element2 = self._add_element_methods(element2)
        return element2

    async def _query_selector_all(self, element, selector):
        selector = self._convert_to_css_if_xpath(selector)
        elements = await element.query_selector_all_async(selector)
        updated_elements = []
        for element in elements:
            element = self._add_element_methods(element)
            updated_elements.append(element)
        await self._slow_mode_pause_if_set()
        return updated_elements

    async def _remove_from_dom(self, element):
        return await element.remove_from_dom_async()

    async def _save_screenshot(self, element, *args, **kwargs):
        return await element.save_screenshot_async(*args, **kwargs)

    async def _save_to_dom(self, element):
        return await element.save_to_dom_async()

    async def _scroll_into_view(self, element):
        await element.scroll_into_view_async()
        await self._add_light_pause()
        return None

    async def _select_option(self, element):
        return await element.select_option_async()

    async def _send_file(self, element, *file_paths):
        return await element.send_file_async(*file_paths)

    async def _send_keys(self, element, text):
        return await element.send_keys_async(text)

    async def _set_text(self, element, value):
        return await element.set_text_async(value)

    async def _set_value(self, element, value):
        return await element.set_value_async(value)

    async def _type(self, element, text):
        with suppress(Exception):
            await element.clear_input()
        await element.send_keys(text)

    async def _get_position(self, element):
        return await element.get_position_async()

    async def _get_html(self, element):
        return await element.get_html_async()

    async def _get_js_attributes(self, element):
        return await element.get_js_attributes_async()

    async def _get_attribute(self, element, attribute):
        try:
            return (await element.get_js_attributes())[attribute]
        except Exception as e:
            if not attribute:
                raise
            try:
                attribute_str = await element.get_js_attributes()
                locate = ' %s="' % attribute
                if locate in attribute_str.outerHTML:
                    outer_html = attribute_str.outerHTML
                    attr_start = outer_html.find(locate) + len(locate)
                    attr_end = outer_html.find('"', attr_start)
                    value = outer_html[attr_start:attr_end]
                    return value
            except Exception:
                pass
        return None

    async def _get_parent(self, element):
        return self._add_element_methods(element.parent)

    async def _get_x_scroll_offset(self):
        x_scroll_offset = await self.page.evaluate("window.pageXOffset")
        return x_scroll_offset or 0

    async def _get_y_scroll_offset(self):
        y_scroll_offset = await self.page.evaluate("window.pageYOffset")
        return y_scroll_offset or 0


class AsyncCDPMethods(Base):
    """异步CDP方法类 - 使用组合模式重构"""

    def __init__(self, page, driver):
        super().__init__(page, driver)
        self._navigation = Navigation(self)
        self.__reg_navigation()
        self._element = Element(self)
        self._action = Action(self)
        self._wait = Wait(self)
        self._page_info = PageInfo(self)
        self._gui = GUI(self)

    def __reg_navigation(self):
        # 导航到指定URL
        self.get = self._navigation.get
        self.open = self._navigation.open
        # 刷新页面
        self.reload = self._navigation.reload
        self.refresh = self._navigation.reload
        # 后退
        self.go_back = self._navigation.go_back
        # 前进
        self.go_forward = self._navigation.go_forward
        # 获取导航历史
        self.history = self._navigation.get_navigation_history
        self.get_navigation_history = self._navigation.get_navigation_history
        # 打开新标签页
        self.open_new_tab = self._navigation.open_new_tab
        # 打开新窗口
        self.open_new_window = self._navigation.open_new_window
        # 切换到指定标签页
        self.switch_to_tab = self._navigation.switch_to_tab
        # 切换到最新的标签页
        self.switch_to_newest_tab = self._navigation.switch_to_newest_tab
        # 切换到指定窗口
        self.switch_to_window = self._navigation.switch_to_window
        # 切换到最新的窗口
        self.switch_to_newest_window = self._navigation.switch_to_newest_window
        # 关闭当前活动的标签页
        self.close_active_tab = self._navigation.close_active_tab
        # 获取当前活动的标签页
        self.get_active_tab = self._navigation.get_active_tab
        # 获取所有标签页
        self.get_tabs = self._navigation.get_tabs
        # 获取窗口信息
        self.get_window = self._navigation.get_window
        # 将活动窗口置于前台
        self.bring_active_window_to_front = self._navigation.bring_active_window_to_front
        # 平铺窗口
        self.tile_windows = self._navigation.tile_windows
        # 授予权限
        self.grant_permissions = self._navigation.grant_permissions
        # 授予所有权限
        self.grant_all_permissions = self._navigation.grant_all_permissions
        # 添加事件处理器
        self.add_handler = self._navigation.add_handler

    # === 元素方法代理 ===
    async def find_element(self, selector, best_match=False, timeout=None):
        """查找单个元素"""
        return await self._element.find_element(selector, best_match=best_match, timeout=timeout)

    async def find_element_by_text(self, text, tag_name=None, timeout=None):
        """通过文本内容查找元素"""
        return await self._element.find_element_by_text(text, tag_name=tag_name, timeout=timeout)

    async def find_elements_by_text(self, text, tag_name=None, timeout=None):
        """通过文本内容查找多个元素"""
        return await self._element.find_elements_by_text(text, tag_name=tag_name, timeout=timeout)

    async def find_all(self, selector, timeout=None):
        """查找所有匹配的元素"""
        return await self._element.find_all(selector, timeout=timeout)

    async def select(self, selector, timeout=None):
        """选择单个元素"""
        return await self._element.select(selector, timeout=timeout)

    async def select_all(self, selector, timeout=None):
        """选择所有匹配的元素"""
        return await self._element.select_all(selector, timeout=timeout)

    async def find_elements(self, selector, timeout=None):
        """查找多个元素"""
        return await self._element.find_elements(selector, timeout=timeout)

    async def find_visible_elements(self, selector, timeout=None):
        """查找所有可见的元素"""
        return await self._element.find_visible_elements(selector, timeout=timeout)

    async def click(self, selector, timeout=None):
        """点击元素"""
        return await self._element.click(selector, timeout=timeout)

    async def click_nth_element(self, selector, number):
        """点击第N个匹配的元素"""
        return await self._element.click_nth_element(selector, number)

    async def click_nth_visible_element(self, selector, number):
        """点击第N个可见的元素"""
        return await self._element.click_nth_visible_element(selector, number)

    async def click_link(self, link_text):
        """点击链接文本"""
        return await self._element.click_link(link_text)

    async def mouse_click(self, selector, timeout=None):
        """模拟鼠标点击"""
        return await self._element.mouse_click(selector, timeout=timeout)

    async def nested_click(self, parent_selector, selector):
        """在父元素内点击子元素"""
        return await self._element.nested_click(parent_selector, selector)

    async def get_nested_element(self, parent_selector, selector):
        """获取父元素内的子元素"""
        return await self._element.get_nested_element(parent_selector, selector)

    async def get_active_element(self):
        """获取当前活动的元素"""
        return await self._element.get_active_element()

    async def get_active_element_css(self):
        """获取当前活动元素的CSS选择器"""
        return await self._element.get_active_element_css()

    # === 动作方法代理 ===
    async def send_keys(self, selector, text, timeout=None):
        """向元素发送按键"""
        return await self._action.send_keys(selector, text, timeout=timeout)

    async def press_keys(self, selector, text, timeout=None):
        """以人类速度按键"""
        return await self._action.press_keys(selector, text, timeout=timeout)

    async def type(self, selector, text, timeout=None):
        """输入文本（先清空字段）"""
        return await self._action.type(selector, text, timeout=timeout)

    async def set_value(self, selector, text, timeout=None):
        """设置元素值"""
        return await self._action.set_value(selector, text, timeout=timeout)

    async def clear(self, selector, timeout=None):
        """清空输入字段"""
        return await self._action.clear(selector, timeout=timeout)

    async def submit(self, selector="form"):
        """提交表单"""
        return await self._action.submit(selector)

    async def get_text(self, selector):
        """获取元素文本"""
        return await self._action.get_text(selector)

    async def get_attribute(self, selector, attribute):
        """获取元素属性值"""
        return await self._action.get_attribute(selector, attribute)

    async def get_element_attribute(self, selector, attribute):
        """获取元素属性值（带异常处理）"""
        return await self._action.get_element_attribute(selector, attribute)

    async def get_element_attributes(self, selector):
        """获取元素所有属性"""
        return await self._action.get_element_attributes(selector)

    async def get_element_html(self, selector):
        """获取元素HTML"""
        return await self._action.get_element_html(selector)

    async def set_attribute(self, selector, attribute, value):
        """设置元素属性"""
        return await self._action.set_attribute(selector, attribute, value)

    async def set_attributes(self, selector, attribute, value):
        """为所有匹配元素设置属性"""
        return await self._action.set_attributes(selector, attribute, value)

    async def remove_attribute(self, selector, attribute):
        """移除元素属性"""
        return await self._action.remove_attribute(selector, attribute)

    async def remove_attributes(self, selector, attribute):
        """移除所有匹配元素的属性"""
        return await self._action.remove_attributes(selector, attribute)

    async def remove_elements(self, selector):
        """移除所有匹配的元素"""
        return await self._action.remove_elements(selector)

    async def set_locale(self, locale):
        """设置语言环境"""
        return await self._action.set_locale(locale)

    async def internalize_links(self):
        """将所有target="_blank"链接改为target="_self" """
        return await self._action.internalize_links()

    # === 等待方法代理 ===
    async def is_element_present(self, selector):
        """检查元素是否存在"""
        return await self._wait.is_element_present(selector)

    async def is_element_visible(self, selector):
        """检查元素是否可见"""
        return await self._wait.is_element_visible(selector)

    async def is_text_visible(self, text, selector="body"):
        """检查文本是否可见"""
        return await self._wait.is_text_visible(text, selector=selector)

    async def is_exact_text_visible(self, text, selector="body"):
        """检查精确文本是否可见"""
        return await self._wait.is_exact_text_visible(text, selector=selector)

    async def wait_for_element_visible(self, selector, timeout=None):
        """等待元素可见"""
        return await self._wait.wait_for_element_visible(selector, timeout=timeout)

    async def wait_for_element_not_visible(self, selector, timeout=None):
        """等待元素不可见"""
        return await self._wait.wait_for_element_not_visible(selector, timeout=timeout)

    async def wait_for_element_absent(self, selector, timeout=None):
        """等待元素从DOM中消失"""
        return await self._wait.wait_for_element_absent(selector, timeout=timeout)

    async def wait_for_text(self, text, selector="body", timeout=None):
        """等待文本出现"""
        return await self._wait.wait_for_text(text, selector=selector, timeout=timeout)

    async def wait_for_text_not_visible(self, text, selector="body", timeout=None):
        """等待文本不可见"""
        return await self._wait.wait_for_text_not_visible(text, selector=selector, timeout=timeout)

    async def assert_element(self, selector, timeout=None):
        """断言元素存在且可见"""
        return await self._wait.assert_element(selector, timeout=timeout)

    async def assert_element_visible(self, selector, timeout=None):
        """断言元素可见"""
        return await self._wait.assert_element_visible(selector, timeout=timeout)

    async def assert_text(self, text, selector="body", timeout=None):
        """断言文本存在"""
        return await self._wait.assert_text(text, selector=selector, timeout=timeout)

    async def assert_exact_text(self, text, selector="body", timeout=None):
        """断言精确文本存在"""
        return await self._wait.assert_exact_text(text, selector=selector, timeout=timeout)

    async def sleep(self, seconds):
        """等待指定秒数"""
        return await self._wait.sleep(seconds)

    async def wait_for(self, selector="", text="", timeout=10):
        """通用等待方法"""
        return await self._wait.wait_for(selector=selector, text=text, timeout=timeout)

    # === 页面信息方法代理 ===
    async def get_title(self):
        """获取页面标题"""
        return await self._page_info.get_title()

    async def get_current_url(self):
        """获取当前URL"""
        return await self._page_info.get_current_url()

    async def get_origin(self):
        """获取页面来源"""
        return await self._page_info.get_origin()

    async def get_page_source(self):
        """获取页面源码"""
        return await self._page_info.get_page_source()

    async def get_user_agent(self):
        """获取用户代理"""
        return await self._page_info.get_user_agent()

    async def get_cookie_string(self):
        """获取Cookie字符串"""
        return await self._page_info.get_cookie_string()

    async def get_locale_code(self):
        """获取语言代码"""
        return await self._page_info.get_locale_code()

    async def get_local_storage_item(self, key):
        """获取localStorage项"""
        return await self._page_info.get_local_storage_item(key)

    async def get_session_storage_item(self, key):
        """获取sessionStorage项"""
        return await self._page_info.get_session_storage_item(key)

    async def get_screen_rect(self):
        """获取屏幕矩形信息"""
        return await self._page_info.get_screen_rect()

    async def get_element_rect(self, selector, timeout=None):
        """获取元素矩形信息"""
        return await self._page_info.get_element_rect(selector, timeout=timeout)

    async def get_gui_element_rect(self, selector, timeout=None):
        """获取GUI元素矩形信息"""
        return await self._page_info.get_gui_element_rect(selector, timeout=timeout)

    async def get_gui_element_center(self, selector, timeout=None):
        """获取GUI元素中心点坐标"""
        return await self._page_info.get_gui_element_center(selector, timeout=timeout)

    async def get_window_rect(self):
        """获取窗口矩形信息"""
        return await self._page_info.get_window_rect()

    async def get_document(self):
        """获取文档对象"""
        return await self._page_info.get_document()

    async def get_flattened_document(self):
        """获取扁平化文档对象"""
        return await self._page_info.get_flattened_document()

    async def evaluate(self, expression):
        """执行JavaScript表达式"""
        return await self._page_info.evaluate(expression)

    async def execute_script(self, script, *args):
        """执行JavaScript脚本"""
        return await self._page_info.execute_script(script, *args)

    async def save_screenshot(self, name, folder=None, selector=None):
        """保存截图"""
        return await self._page_info.save_screenshot(name, folder=folder, selector=selector)

    async def print_to_pdf(self, name, folder=None):
        """打印为PDF"""
        return await self._page_info.print_to_pdf(name, folder=folder)

    async def scroll_into_view(self, selector):
        """滚动元素到可视区域"""
        return await self._page_info.scroll_into_view(selector)

    async def scroll_to_y(self, y):
        """滚动到指定Y坐标"""
        return await self._page_info.scroll_to_y(y)

    async def scroll_to_top(self):
        """滚动到页面顶部"""
        return await self._page_info.scroll_to_top()

    async def scroll_to_bottom(self):
        """滚动到页面底部"""
        return await self._page_info.scroll_to_bottom()

    async def scroll_up(self, amount=25):
        """向上滚动"""
        return await self._page_info.scroll_up(amount=amount)

    async def scroll_down(self, amount=25):
        """向下滚动"""
        return await self._page_info.scroll_down(amount=amount)

    async def set_local_storage_item(self, key, value):
        """设置localStorage项"""
        return await self._page_info.set_local_storage_item(key, value)

    async def set_session_storage_item(self, key, value):
        """设置sessionStorage项"""
        return await self._page_info.set_session_storage_item(key, value)

    # === GUI方法代理 ===
    async def gui_press_key(self, key):
        """GUI按键"""
        return await self._gui.gui_press_key(key)

    async def gui_press_keys(self, keys):
        """GUI按多个键"""
        return await self._gui.gui_press_keys(keys)

    async def gui_write(self, text):
        """GUI写入文本"""
        return await self._gui.gui_write(text)

    async def gui_click_x_y(self, x, y, timeframe=0.25):
        """GUI点击坐标"""
        return await self._gui.gui_click_x_y(x, y, timeframe=timeframe)

    async def gui_click_element(self, selector, timeframe=0.25):
        """GUI点击元素"""
        return await self._gui.gui_click_element(selector, timeframe=timeframe)

    async def gui_drag_drop_points(self, x1, y1, x2, y2, timeframe=0.35):
        """GUI拖拽坐标点"""
        return await self._gui.gui_drag_drop_points(x1, y1, x2, y2, timeframe=timeframe)

    async def gui_drag_and_drop(self, drag_selector, drop_selector, timeframe=0.35):
        """GUI拖拽元素"""
        return await self._gui.gui_drag_and_drop(drag_selector, drop_selector, timeframe=timeframe)

    async def gui_click_and_hold(self, selector, timeframe=0.35):
        """GUI点击并保持"""
        return await self._gui.gui_click_and_hold(selector, timeframe=timeframe)

    async def gui_hover_x_y(self, x, y, timeframe=0.25):
        """GUI悬停坐标"""
        return await self._gui.gui_hover_x_y(x, y, timeframe=timeframe)

    async def gui_hover_element(self, selector, timeframe=0.25):
        """GUI悬停元素"""
        return await self._gui.gui_hover_element(selector, timeframe=timeframe)

    async def gui_hover_and_click(self, hover_selector, click_selector):
        """GUI悬停并点击"""
        return await self._gui.gui_hover_and_click(hover_selector, click_selector)


class SyncCDPMethods(AsyncCDPMethods):
    """同步CDP方法类 - 包装异步方法"""

    def __init__(self, loop, page, driver):
        self.loop = loop
        self.doing = False
        super(SyncCDPMethods, self).__init__(page, driver)

    def __getattribute__(self, name):
        """动态代理所有方法到异步版本"""
        attr = super().__getattribute__(name)
        if asyncio.iscoroutinefunction(attr):
            # 如果是协程函数，包装为同步调用
            def sync_wrapper(*args, **kwargs):
                return self._run_sync(attr(*args, **kwargs))
            return sync_wrapper
        return attr

    def _run_sync(self, coro):
        """同步执行协程"""
        if not self.doing:
            try:
                self.doing = True
                return self.loop.run_until_complete(coro)
            finally:
                self.doing = False
        return coro
