"""
SBCDP API Mixins - 定义所有混入类的接口
提供类型提示和IDE支持
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, Union, List, TYPE_CHECKING

if TYPE_CHECKING:
    # 仅在类型检查时导入，避免循环导入
    from ..driver.tab import Tab
    from ..driver.browser import Browser


class BaseMixin(ABC):
    """基础混入类接口"""
    
    # 这些属性会在实际的组合类中被设置
    page: 'Tab'
    driver: 'Browser'
    
    @abstractmethod
    async def _slow_mode_pause_if_set(self) -> None:
        """根据配置添加慢速模式暂停"""
        ...
    
    @abstractmethod
    async def _add_light_pause(self) -> None:
        """添加轻微暂停"""
        ...
    
    @abstractmethod
    def _convert_to_css_if_xpath(self, selector: str) -> str:
        """如果是XPath选择器，尝试转换为CSS选择器"""
        ...
    
    @abstractmethod
    def _add_element_methods(self, element: Any) -> Any:
        """为元素添加额外的方法"""
        ...


class DOMInterface(BaseMixin):
    """DOM操作接口"""
    
    @abstractmethod
    async def find_element(self, selector: str, best_match: bool = False, timeout: Optional[float] = None) -> Any:
        """查找元素"""
        ...
    
    @abstractmethod
    async def select(self, selector: str, timeout: Optional[float] = None) -> Any:
        """选择元素"""
        ...


class NavigationInterface(BaseMixin):
    """导航操作接口"""
    
    @abstractmethod
    async def get(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs) -> None:
        """导航到URL"""
        ...
    
    @abstractmethod
    async def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None) -> None:
        """重新加载页面"""
        ...


class ActionInterface(BaseMixin):
    """动作操作接口"""
    
    @abstractmethod
    async def click(self, selector: str, timeout: Optional[float] = None) -> None:
        """点击元素"""
        ...
    
    @abstractmethod
    async def type(self, selector: str, text: str, timeout: Optional[float] = None) -> None:
        """输入文本"""
        ...


class WaitInterface(BaseMixin):
    """等待操作接口"""
    
    @abstractmethod
    async def wait_for_element_visible(self, selector: str, timeout: Optional[float] = None) -> Any:
        """等待元素可见"""
        ...
    
    @abstractmethod
    async def assert_element(self, selector: str, timeout: Optional[float] = None) -> None:
        """断言元素存在"""
        ...


class GUIInterface(BaseMixin):
    """GUI操作接口"""
    
    @abstractmethod
    async def gui_click_x_y(self, x: float, y: float, timeframe: Optional[float] = None) -> None:
        """GUI点击坐标"""
        ...


class PageInfoInterface(BaseMixin):
    """页面信息接口"""
    
    @abstractmethod
    async def get_title(self) -> str:
        """获取页面标题"""
        ...
    
    @abstractmethod
    async def get_current_url(self) -> str:
        """获取当前URL"""
        ...


# 组合接口，包含所有功能
class CDPMethodsInterface(
    DOMInterface,
    NavigationInterface, 
    ActionInterface,
    WaitInterface,
    GUIInterface,
    PageInfoInterface
):
    """完整的CDP方法接口"""
    pass
