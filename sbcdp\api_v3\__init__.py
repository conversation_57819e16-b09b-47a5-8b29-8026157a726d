"""
SBCDP API v3 - 使用建造者模式 + 流式接口
提供更流畅的API体验
"""

import asyncio
import sys
from contextlib import suppress
from typing import Optional, Any, List, Union

from .. import settings
from ..fixtures import shared_utils, constants, page_utils, js_utils


class FluentCDPMethods:
    """流式CDP方法类"""

    def __init__(self, page, driver):
        self.page = page
        self.driver = driver
        self._current_element = None

    # === 基础工具方法 ===
    async def _slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        if (
            (hasattr(self.driver.config, "demo_mode") and self.driver.config.demo_mode)
            or "--demo" in sys.argv
        ):
            await asyncio.sleep(0.48)
        elif (
            (hasattr(self.driver.config, "slow_mode") and self.driver.config.slow_mode)
            or "--slow" in sys.argv
        ):
            await asyncio.sleep(0.24)

    async def _add_light_pause(self):
        """添加轻微暂停"""
        await asyncio.sleep(0.007)

    def _convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        if page_utils.is_xpath_selector(selector):
            with suppress(Exception):
                css = js_utils.convert_to_css_selector(selector, "xpath")
                if css:
                    return css
        return selector

    def _add_element_methods(self, element):
        """为元素添加额外的方法"""
        if not element:
            return element

        # 检查是否已经添加过方法
        if hasattr(element, '_methods_added'):
            return element

        # 只添加text_all属性，不覆盖其他方法
        if not hasattr(element, 'text_all'):
            async def get_text_all():
                try:
                    return element.text or ""
                except Exception:
                    return ""
            element.text_all = get_text_all

        # 标记已添加方法
        element._methods_added = True

        return element

    # === 向后兼容的直接方法 ===
    async def get(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs):
        """导航到URL（向后兼容）"""
        return await self.navigate().to(url, new_tab=new_tab, new_window=new_window, **kwargs)

    async def find_element(self, selector: str, timeout: Optional[float] = None):
        """查找元素（向后兼容）"""
        return await self.find(selector).get_element(timeout=timeout)



    async def click(self, selector: str, timeout: Optional[float] = None):
        """点击元素（向后兼容）"""
        await self.find(selector).click(timeout=timeout)
        return self

    async def type(self, selector: str, text: str, timeout: Optional[float] = None):
        """输入文本（向后兼容）"""
        await self.find(selector).type(text, timeout=timeout)
        return self

    async def get_text(self, selector: str, timeout: Optional[float] = None):
        """获取文本（向后兼容）"""
        return await self.find(selector).get_text(timeout=timeout)

    async def get_attribute(self, selector: str, attribute: str, timeout: Optional[float] = None):
        """获取属性（向后兼容）"""
        return await self.find(selector).get_attribute(attribute, timeout=timeout)

    async def get_title(self):
        """获取页面标题（向后兼容）"""
        return await self.page_info().title()

    async def get_current_url(self):
        """获取当前URL（向后兼容）"""
        return await self.page_info().url()

    async def evaluate(self, script: str):
        """执行JavaScript（向后兼容）"""
        return await self.page_info().evaluate(script)

    async def set_value(self, selector: str, value: str, timeout: Optional[float] = None):
        """设置值（向后兼容）"""
        await self.find(selector).set_value(value, timeout=timeout)
        return self

    async def is_element_present(self, selector: str, timeout: Optional[float] = None):
        """检查元素是否存在（向后兼容）"""
        return await self.find(selector).is_present(timeout=timeout)

    async def save_screenshot(self, filename: str = "screenshot.png"):
        """保存截图（向后兼容）"""
        return await self.page_info().save_screenshot(filename)

    async def find_elements(self, selector: str, timeout: Optional[float] = None):
        """查找多个元素（向后兼容）"""
        return await self.find(selector).get_all_elements(timeout=timeout)

    async def wait_for_element_visible(self, selector: str, timeout: Optional[float] = None):
        """等待元素可见（向后兼容）"""
        return await self.wait().for_element_visible(selector, timeout=timeout)

    async def assert_element(self, selector: str, timeout: Optional[float] = None):
        """断言元素存在（向后兼容）"""
        await self.wait().assert_element(selector, timeout=timeout)
        return self

    async def assert_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None):
        """断言文本存在（向后兼容）"""
        await self.wait().assert_text(text, selector=selector, timeout=timeout)
        return self

    async def scroll_to_top(self):
        """滚动到页面顶部（向后兼容）"""
        await self.page.evaluate("window.scrollTo(0, 0)")
        await self._add_light_pause()
        return self

    async def scroll_to_bottom(self):
        """滚动到页面底部（向后兼容）"""
        await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await self._add_light_pause()
        return self

    async def scroll_into_view(self, selector: str, timeout: Optional[float] = None):
        """滚动元素到可视区域（向后兼容）"""
        await self.find(selector).scroll_into_view(timeout=timeout)
        return self

    async def flash(self, selector: str, timeout: Optional[float] = None):
        """闪烁高亮元素（向后兼容）"""
        await self.find(selector).flash(timeout=timeout)
        return self

    async def send_keys(self, selector: str, text: str, timeout: Optional[float] = None):
        """发送按键（向后兼容）"""
        await self.find(selector).send_keys(text, timeout=timeout)
        return self

    async def clear(self, selector: str, timeout: Optional[float] = None):
        """清空元素（向后兼容）"""
        await self.find(selector).clear(timeout=timeout)
        return self

    async def is_element_visible(self, selector: str, timeout: Optional[float] = None):
        """检查元素是否可见（向后兼容）"""
        return await self.find(selector).is_visible(timeout=timeout)

    async def get_page_source(self):
        """获取页面源码（向后兼容）"""
        return await self.page_info().source()

    async def get_window_rect(self):
        """获取窗口矩形信息（向后兼容）"""
        return await self.page_info().get_window_rect()

    async def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None):
        """重新加载页面（向后兼容）"""
        await self.navigate().reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load)
        return self

    async def go_back(self):
        """后退（向后兼容）"""
        await self.navigate().back()
        return self

    async def go_forward(self):
        """前进（向后兼容）"""
        await self.navigate().forward()
        return self

    # === 建造者方法 ===
    def navigate(self):
        """返回导航操作构建器"""
        return NavigationBuilder(self)

    def find(self, selector: str):
        """查找元素并返回元素操作构建器"""
        return ElementBuilder(self, selector)

    def select(self, selector: str):
        """选择元素（find的别名）"""
        return self.find(selector)

    def wait(self):
        """返回等待操作构建器"""
        return WaitBuilder(self)

    def page_info(self):
        """返回页面信息构建器"""
        return PageInfoBuilder(self)

    def gui(self):
        """返回GUI操作构建器"""
        return GUIBuilder(self)


class NavigationBuilder:
    """导航操作构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def to(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs):
        """导航到URL"""
        url = shared_utils.fix_url_as_needed(url)
        await self.methods.page.get(url, new_tab=new_tab, new_window=new_window, **kwargs)
        url_protocol = url.split(":")[0]
        safe_url = True
        if url_protocol not in ["about", "data", "chrome"]:
            safe_url = False
        if not safe_url:
            await asyncio.sleep(constants.UC.CDP_MODE_OPEN_WAIT)
            if shared_utils.is_windows():
                await asyncio.sleep(constants.UC.EXTRA_WINDOWS_WAIT)
        else:
            await asyncio.sleep(0.012)
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None):
        """重新加载页面"""
        await self.methods.page.reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load)
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def back(self):
        """后退"""
        await self.methods.page.go_back()
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def forward(self):
        """前进"""
        await self.methods.page.go_forward()
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods


class ElementBuilder:
    """元素操作构建器"""

    def __init__(self, methods, selector: str):
        self.methods = methods
        self.selector = selector
        self._element = None
        self._timeout = None

    async def _get_element(self, timeout: Optional[float] = None):
        """获取元素（懒加载）"""
        if timeout is None:
            timeout = self._timeout or settings.SMALL_TIMEOUT

        await self.methods._add_light_pause()
        selector = self.methods._convert_to_css_if_xpath(self.selector)

        try:
            element = await self.methods.page.find(selector, timeout=timeout)
            element = self.methods._add_element_methods(element)
            await self.methods._slow_mode_pause_if_set()
            return element
        except Exception:
            plural = "s" if timeout != 1 else ""
            message = f"\n Element {{{self.selector}}} was not found after {timeout} second{plural}!"
            raise Exception(message)

    async def get_element(self, timeout: Optional[float] = None):
        """获取元素（公共方法）"""
        return await self._get_element(timeout)

    async def get_all_elements(self, timeout: Optional[float] = None):
        """获取所有匹配的元素"""
        if timeout is None:
            timeout = settings.SMALL_TIMEOUT

        await self.methods._add_light_pause()
        selector = self.methods._convert_to_css_if_xpath(self.selector)

        elements = await self.methods.page.find_elements(selector, timeout=timeout)
        updated_elements = []
        for element in elements:
            element = self.methods._add_element_methods(element)
            updated_elements.append(element)
        await self.methods._slow_mode_pause_if_set()
        return updated_elements

    async def click(self, timeout: Optional[float] = None):
        """点击元素"""
        element = await self._get_element(timeout)
        await element.scroll_into_view()
        await element.click()
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def type(self, text: str, timeout: Optional[float] = None):
        """输入文本"""
        element = await self._get_element(timeout)
        await element.scroll_into_view()

        # 清空输入框
        with suppress(Exception):
            await element.clear_input()

        # 处理换行符
        if text.endswith("\n") or text.endswith("\r"):
            text = text[:-1] + "\r\n"

        # 发送按键
        await element.send_keys(text)
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.sleep(0.025)
        return self.methods

    async def send_keys(self, text: str, timeout: Optional[float] = None):
        """发送按键"""
        element = await self._get_element(timeout)
        await element.send_keys(text)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def clear(self, timeout: Optional[float] = None):
        """清空元素"""
        element = await self._get_element(timeout)
        await element.clear()
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def set_value(self, value: str, timeout: Optional[float] = None):
        """设置值"""
        element = await self._get_element(timeout)
        await element.set_value(value)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def get_text(self, timeout: Optional[float] = None) -> str:
        """获取文本"""
        element = await self._get_element(timeout)
        try:
            # 直接使用元素的text属性
            return element.text or ""
        except Exception:
            return ""

    async def get_attribute(self, name: str, timeout: Optional[float] = None) -> str:
        """获取属性"""
        element = await self._get_element(timeout)
        return await element.get_attribute(name)

    async def is_visible(self, timeout: Optional[float] = None) -> bool:
        """检查是否可见"""
        try:
            element = await self._get_element(timeout or 0.1)
            return element.is_displayed()
        except Exception:
            return False

    async def is_present(self, timeout: Optional[float] = None) -> bool:
        """检查是否存在"""
        try:
            await self._get_element(timeout or 0.1)
            return True
        except Exception:
            return False

    async def scroll_into_view(self, timeout: Optional[float] = None):
        """滚动到可视区域"""
        element = await self._get_element(timeout)
        await element.scroll_into_view()
        await self.methods._add_light_pause()
        return self.methods

    async def flash(self, timeout: Optional[float] = None):
        """闪烁高亮"""
        element = await self._get_element(timeout)
        await element.flash()
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    def and_then(self):
        """链式操作连接符"""
        return self.methods


class WaitBuilder:
    """等待操作构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def for_element(self, selector: str, timeout: Optional[float] = None):
        """等待元素出现"""
        if timeout is None:
            timeout = settings.LARGE_TIMEOUT

        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                element = await self.methods.find(selector).get_element(timeout=0.5)
                if element.is_displayed():
                    return ElementBuilder(self.methods, selector)
            except Exception:
                pass
            await asyncio.sleep(0.1)

        raise Exception(f"Element {selector} was not visible after {timeout} seconds")

    async def for_element_visible(self, selector: str, timeout: Optional[float] = None):
        """等待元素可见"""
        return await self.for_element(selector, timeout)

    async def for_element_absent(self, selector: str, timeout: Optional[float] = None):
        """等待元素消失"""
        if timeout is None:
            timeout = settings.LARGE_TIMEOUT

        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                await self.methods.find(selector).get_element(timeout=0.5)
                await asyncio.sleep(0.1)
            except Exception:
                return self.methods  # 元素不存在，等待成功

        raise Exception(f"Element {selector} was still present after {timeout} seconds")

    async def for_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None):
        """等待文本出现"""
        if timeout is None:
            timeout = settings.LARGE_TIMEOUT

        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                if selector:
                    # 在指定元素中查找文本
                    element = await self.methods.find(selector).get_element(timeout=0.5)
                    element_text = await element.text_content()
                    if text in element_text:
                        return self.methods
                else:
                    # 在整个页面中查找文本
                    page_text = await self.methods.page.evaluate("document.body.innerText")
                    if text in page_text:
                        return self.methods
            except Exception:
                pass
            await asyncio.sleep(0.1)

        raise Exception(f"Text '{text}' was not found after {timeout} seconds")

    async def assert_element(self, selector: str, timeout: Optional[float] = None):
        """断言元素存在"""
        if timeout is None:
            timeout = settings.SMALL_TIMEOUT

        try:
            await self.methods.find(selector).get_element(timeout=timeout)
            return self.methods
        except Exception:
            raise AssertionError(f"Element {selector} was not found")

    async def assert_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None):
        """断言文本存在"""
        if timeout is None:
            timeout = settings.SMALL_TIMEOUT

        if selector:
            # 在指定元素中查找文本
            element = await self.methods.find(selector).get_element(timeout=timeout)
            element_text = await element.text_content()
            if text not in element_text:
                raise AssertionError(f"Text '{text}' not found in element {selector}")
        else:
            # 在整个页面中查找文本
            page_text = await self.methods.page.evaluate("document.body.innerText")
            if text not in page_text:
                raise AssertionError(f"Text '{text}' not found on page")

        return self.methods

    async def seconds(self, duration: float):
        """等待指定秒数"""
        await asyncio.sleep(duration)
        return self.methods


class PageInfoBuilder:
    """页面信息构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def title(self) -> str:
        """获取页面标题"""
        return await self.methods.page.evaluate("document.title")

    async def url(self) -> str:
        """获取当前URL"""
        return await self.methods.page.evaluate("window.location.href")

    async def source(self) -> str:
        """获取页面源码"""
        return await self.methods.page.evaluate("document.documentElement.outerHTML")

    async def evaluate(self, script: str):
        """执行JavaScript代码"""
        return await self.methods.page.evaluate(script)

    async def execute_script(self, script: str):
        """执行JavaScript代码（别名）"""
        return await self.evaluate(script)

    async def get_window_rect(self):
        """获取窗口矩形信息"""
        return await self.methods.page.evaluate("""
            () => {
                return {
                    x: window.screenX,
                    y: window.screenY,
                    width: window.outerWidth,
                    height: window.outerHeight,
                    innerWidth: window.innerWidth,
                    innerHeight: window.innerHeight,
                    pageXOffset: window.pageXOffset,
                    pageYOffset: window.pageYOffset,
                    scrollX: window.scrollX,
                    scrollY: window.scrollY
                };
            }
        """)

    async def save_screenshot(self, filename: str = "screenshot.png"):
        """保存截图"""
        screenshot_data = await self.methods.page.screenshot()
        with open(filename, 'wb') as f:
            f.write(screenshot_data)
        return filename

    async def localStorage_get_item(self, key: str):
        """获取localStorage项"""
        return await self.methods.page.evaluate(f"localStorage.getItem('{key}')")

    async def localStorage_set_item(self, key: str, value: str):
        """设置localStorage项"""
        await self.methods.page.evaluate(f"localStorage.setItem('{key}', '{value}')")
        return self.methods

    async def sessionStorage_get_item(self, key: str):
        """获取sessionStorage项"""
        return await self.methods.page.evaluate(f"sessionStorage.getItem('{key}')")

    async def sessionStorage_set_item(self, key: str, value: str):
        """设置sessionStorage项"""
        await self.methods.page.evaluate(f"sessionStorage.setItem('{key}', '{value}')")
        return self.methods


class GUIBuilder:
    """GUI操作构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def click_at(self, x: float, y: float, timeframe: Optional[float] = None):
        """在坐标点击"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        if not timeframe:
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3

        # 移动到目标位置
        pyautogui.moveTo(x, y, duration=timeframe)
        await asyncio.sleep(0.1)

        # 点击
        pyautogui.click(x, y)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def hover_at(self, x: float, y: float, timeframe: Optional[float] = None):
        """在坐标悬停"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        if not timeframe:
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3

        pyautogui.moveTo(x, y, duration=timeframe)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def press_key(self, key: str):
        """按键"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        pyautogui.press(key)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def write(self, text: str, interval: float = 0.0):
        """输入文本"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        pyautogui.write(text, interval=interval)
        await self.methods._slow_mode_pause_if_set()
        return self.methods


# 同步包装器
class SyncFluentCDPMethods:
    """同步流式CDP方法类"""

    def __init__(self, loop, page, driver):
        self.loop = loop
        self.async_methods = FluentCDPMethods(page, driver)
        self.doing = False

    def _run_sync(self, coro):
        """同步执行协程"""
        if not self.doing:
            try:
                self.doing = True
                return self.loop.run_until_complete(coro)
            finally:
                self.doing = False
        return coro

    # 向后兼容的同步方法
    def get(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs):
        return self._run_sync(self.async_methods.get(url, new_tab=new_tab, new_window=new_window, **kwargs))

    def find_element(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.find_element(selector, timeout=timeout))

    def click(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.click(selector, timeout=timeout))

    def type(self, selector: str, text: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.type(selector, text, timeout=timeout))

    def get_text(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.get_text(selector, timeout=timeout))

    def get_title(self):
        return self._run_sync(self.async_methods.get_title())

    def get_current_url(self):
        return self._run_sync(self.async_methods.get_current_url())

    def evaluate(self, script: str):
        return self._run_sync(self.async_methods.evaluate(script))

    def save_screenshot(self, filename: str = "screenshot.png"):
        return self._run_sync(self.async_methods.save_screenshot(filename))

    def find_elements(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.find_elements(selector, timeout=timeout))

    def get_attribute(self, selector: str, attribute: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.get_attribute(selector, attribute, timeout=timeout))

    def set_value(self, selector: str, value: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.set_value(selector, value, timeout=timeout))

    def is_element_present(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.is_element_present(selector, timeout=timeout))

    def is_element_visible(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.is_element_visible(selector, timeout=timeout))

    def get_page_source(self):
        return self._run_sync(self.async_methods.get_page_source())

    def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None):
        return self._run_sync(self.async_methods.reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load))

    def send_keys(self, selector: str, text: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.send_keys(selector, text, timeout=timeout))

    def clear(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.clear(selector, timeout=timeout))

    def scroll_to_top(self):
        return self._run_sync(self.async_methods.scroll_to_top())

    def scroll_to_bottom(self):
        return self._run_sync(self.async_methods.scroll_to_bottom())

    def scroll_into_view(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.scroll_into_view(selector, timeout=timeout))

    def flash(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.flash(selector, timeout=timeout))


# 别名，保持向后兼容
AsyncCDPMethods = FluentCDPMethods
SyncCDPMethods = SyncFluentCDPMethods
