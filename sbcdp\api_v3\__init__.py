"""
SBCDP API v3 - 使用建造者模式 + 流式接口
提供更流畅的API体验
"""

import asyncio
from typing import Optional, Any


class FluentCDPMethods:
    """流式CDP方法类"""
    
    def __init__(self, page, driver):
        self.page = page
        self.driver = driver
        self._current_element = None
    
    # === 导航操作 ===
    def navigate(self):
        """返回导航操作构建器"""
        return NavigationBuilder(self)
    
    # === DOM操作 ===
    def find(self, selector: str):
        """查找元素并返回元素操作构建器"""
        return ElementBuilder(self, selector)
    
    def select(self, selector: str):
        """选择元素（find的别名）"""
        return self.find(selector)
    
    # === 等待操作 ===
    def wait(self):
        """返回等待操作构建器"""
        return WaitBuilder(self)
    
    # === 页面信息 ===
    def page_info(self):
        """返回页面信息构建器"""
        return PageInfoBuilder(self)
    
    # === GUI操作 ===
    def gui(self):
        """返回GUI操作构建器"""
        return GUIBuilder(self)


class NavigationBuilder:
    """导航操作构建器"""
    
    def __init__(self, methods):
        self.methods = methods
    
    async def to(self, url: str, new_tab: bool = False, new_window: bool = False):
        """导航到URL"""
        # 实际的导航逻辑
        await self.methods.page.get(url, new_tab=new_tab, new_window=new_window)
        return self.methods
    
    async def reload(self, ignore_cache: bool = True):
        """重新加载页面"""
        await self.methods.page.reload(ignore_cache=ignore_cache)
        return self.methods
    
    async def back(self):
        """后退"""
        await self.methods.page.go_back()
        return self.methods
    
    async def forward(self):
        """前进"""
        await self.methods.page.go_forward()
        return self.methods


class ElementBuilder:
    """元素操作构建器"""
    
    def __init__(self, methods, selector: str):
        self.methods = methods
        self.selector = selector
        self._element = None
    
    async def _get_element(self):
        """获取元素（懒加载）"""
        if self._element is None:
            self._element = await self.methods.page.find(self.selector)
        return self._element
    
    async def click(self):
        """点击元素"""
        element = await self._get_element()
        await element.click()
        return self.methods
    
    async def type(self, text: str):
        """输入文本"""
        element = await self._get_element()
        await element.clear()
        await element.send_keys(text)
        return self.methods
    
    async def clear(self):
        """清空元素"""
        element = await self._get_element()
        await element.clear()
        return self.methods
    
    async def get_text(self) -> str:
        """获取文本"""
        element = await self._get_element()
        return await element.text_content()
    
    async def get_attribute(self, name: str) -> str:
        """获取属性"""
        element = await self._get_element()
        return await element.get_attribute(name)
    
    async def is_visible(self) -> bool:
        """检查是否可见"""
        try:
            element = await self._get_element()
            return element.is_displayed()
        except Exception:
            return False
    
    def and_then(self):
        """链式操作连接符"""
        return self.methods


class WaitBuilder:
    """等待操作构建器"""
    
    def __init__(self, methods):
        self.methods = methods
    
    async def for_element(self, selector: str, timeout: Optional[float] = None):
        """等待元素出现"""
        # 等待逻辑
        await asyncio.sleep(0.1)  # 占位实现
        return ElementBuilder(self.methods, selector)
    
    async def for_text(self, text: str, timeout: Optional[float] = None):
        """等待文本出现"""
        # 等待逻辑
        await asyncio.sleep(0.1)  # 占位实现
        return self.methods
    
    async def seconds(self, duration: float):
        """等待指定秒数"""
        await asyncio.sleep(duration)
        return self.methods


class PageInfoBuilder:
    """页面信息构建器"""
    
    def __init__(self, methods):
        self.methods = methods
    
    async def title(self) -> str:
        """获取页面标题"""
        return await self.methods.page.evaluate("document.title")
    
    async def url(self) -> str:
        """获取当前URL"""
        return await self.methods.page.evaluate("window.location.href")
    
    async def source(self) -> str:
        """获取页面源码"""
        return await self.methods.page.evaluate("document.documentElement.outerHTML")


class GUIBuilder:
    """GUI操作构建器"""
    
    def __init__(self, methods):
        self.methods = methods
    
    async def click_at(self, x: float, y: float):
        """在坐标点击"""
        try:
            import pyautogui
            pyautogui.click(x, y)
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations")
        return self.methods
    
    async def press_key(self, key: str):
        """按键"""
        try:
            import pyautogui
            pyautogui.press(key)
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations")
        return self.methods


# 使用示例
async def fluent_example():
    """流式API使用示例"""
    page = None  # 实际使用时从Chrome实例获取
    driver = None
    
    methods = FluentCDPMethods(page, driver)
    
    # 流式操作链
    await (methods
           .navigate().to("https://example.com")
           .find("input[name='search']").type("hello world")
           .find("button[type='submit']").click()
           .wait().for_text("Results")
           .find(".result").click())
    
    # 获取信息
    title = await methods.page_info().title()
    url = await methods.page_info().url()
    
    # 条件操作
    if await methods.find("div.error").is_visible():
        await methods.find("button.close").click()
    
    return methods
