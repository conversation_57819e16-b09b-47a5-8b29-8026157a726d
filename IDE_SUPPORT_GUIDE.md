# SBCDP IDE 支持指南

## 问题描述
在将 `AsyncCDPMethods` 类拆分为多个模块后，IDE（如PyCharm、VSCode）无法正确解析跨类的方法调用，导致无法跳转到方法定义，影响开发体验。

## 解决方案

### 1. 类型提示方案（已实施）
我们为每个API模块添加了 `TYPE_CHECKING` 条件导入和类型提示：

```python
from typing import TYPE_CHECKING

class DOM:
    if TYPE_CHECKING:
        # 类型提示：这些方法在多重继承中会被提供
        async def _slow_mode_pause_if_set(self) -> None: ...
        async def _add_light_pause(self) -> None: ...
        def _convert_to_css_if_xpath(self, selector: str) -> str: ...
        def _add_element_methods(self, element: Any) -> Any: ...
        page: Any
        driver: Any
```

### 2. IDE 配置优化

#### VSCode 配置
已创建 `.vscode/settings.json` 文件，包含：
- 启用基础类型检查
- 自动导入补全
- MyPy 集成

#### MyPy 配置
已创建 `mypy.ini` 文件，配置类型检查规则。

### 3. 开发最佳实践

#### 方法跳转
现在IDE应该能够正确跳转到：
- ✅ 基础方法（如 `_slow_mode_pause_if_set`）
- ✅ 跨模块方法调用（如 `find_element`）
- ✅ 属性访问（如 `self.page`、`self.driver`）

#### 代码补全
IDE现在提供：
- ✅ 方法名自动补全
- ✅ 参数类型提示
- ✅ 返回值类型提示

### 4. 故障排除

#### 如果IDE仍然无法跳转：

1. **重启IDE**：重新加载类型信息
2. **清除缓存**：删除 `__pycache__` 文件夹
3. **检查Python解释器**：确保IDE使用正确的Python环境
4. **更新IDE**：确保使用最新版本的IDE

#### 常见问题：

**Q: 为什么使用 `TYPE_CHECKING` 而不是直接导入？**
A: `TYPE_CHECKING` 只在类型检查时为真，避免了运行时的循环导入问题，同时为IDE提供了类型信息。

**Q: 这会影响运行时性能吗？**
A: 不会。`TYPE_CHECKING` 块中的代码只在类型检查时执行，运行时会被忽略。

**Q: 如何添加新的方法类型提示？**
A: 在对应模块的 `TYPE_CHECKING` 块中添加方法签名即可。

### 5. 文件结构

```
sbcdp/api/
├── __init__.py          # 主要组合类
├── base.py             # 基础方法实现
├── dom.py              # DOM操作（已添加类型提示）
├── navigation.py       # 导航操作（已添加类型提示）
├── action.py           # 动作操作（已添加类型提示）
├── gui.py              # GUI操作（已添加类型提示）
├── page_info.py        # 页面信息（已添加类型提示）
├── wait.py             # 等待操作（已添加类型提示）
├── mixins.py           # 接口定义（可选）
└── types.py            # 协议定义（可选）
```

### 6. 验证IDE支持

运行以下测试来验证IDE支持是否正常：

```python
# 测试文件：test_ide_support.py
from sbcdp.api import AsyncCDPMethods

async def test_ide_support():
    # 创建实例（需要mock对象）
    methods = AsyncCDPMethods(None, None)
    
    # 测试方法跳转（Ctrl+Click应该能跳转）
    await methods._slow_mode_pause_if_set()  # 应该跳转到base.py
    await methods.find_element("div")        # 应该跳转到dom.py
    await methods.click("button")            # 应该跳转到action.py
    await methods.get("https://example.com") # 应该跳转到navigation.py
```

### 7. 总结

通过添加 `TYPE_CHECKING` 类型提示，我们成功解决了多重继承中的IDE跳转问题，同时保持了：
- ✅ 运行时性能不受影响
- ✅ 代码结构清晰
- ✅ 开发体验良好
- ✅ 类型安全

现在您可以享受完整的IDE支持，包括方法跳转、代码补全和类型检查！
