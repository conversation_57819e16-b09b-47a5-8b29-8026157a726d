"""
SBCDP API v3 页面信息构建器
处理页面信息获取和JavaScript执行
"""

from typing import Optional


class PageInfoBuilder:
    """页面信息构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def title(self) -> str:
        """获取页面标题"""
        return await self.methods.page.evaluate("document.title")

    async def url(self) -> str:
        """获取当前URL"""
        return await self.methods.page.evaluate("window.location.href")

    async def source(self) -> str:
        """获取页面源码"""
        return await self.methods.page.evaluate("document.documentElement.outerHTML")

    async def evaluate(self, script: str):
        """执行JavaScript代码"""
        return await self.methods.page.evaluate(script)

    async def execute_script(self, script: str):
        """执行JavaScript代码（别名）"""
        return await self.evaluate(script)

    async def get_window_rect(self):
        """获取窗口矩形信息"""
        return await self.methods.page.evaluate("""
            () => {
                return {
                    x: window.screenX,
                    y: window.screenY,
                    width: window.outerWidth,
                    height: window.outerHeight,
                    innerWidth: window.innerWidth,
                    innerHeight: window.innerHeight,
                    pageXOffset: window.pageXOffset,
                    pageYOffset: window.pageYOffset,
                    scrollX: window.scrollX,
                    scrollY: window.scrollY
                };
            }
        """)

    async def save_screenshot(self, filename: str = "screenshot.png"):
        """保存截图"""
        screenshot_data = await self.methods.page.screenshot()
        with open(filename, 'wb') as f:
            f.write(screenshot_data)
        return filename

    async def localStorage_get_item(self, key: str):
        """获取localStorage项"""
        return await self.methods.page.evaluate(f"localStorage.getItem('{key}')")

    async def localStorage_set_item(self, key: str, value: str):
        """设置localStorage项"""
        await self.methods.page.evaluate(f"localStorage.setItem('{key}', '{value}')")
        return self.methods

    async def sessionStorage_get_item(self, key: str):
        """获取sessionStorage项"""
        return await self.methods.page.evaluate(f"sessionStorage.getItem('{key}')")

    async def sessionStorage_set_item(self, key: str, value: str):
        """设置sessionStorage项"""
        await self.methods.page.evaluate(f"sessionStorage.setItem('{key}', '{value}')")
        return self.methods
