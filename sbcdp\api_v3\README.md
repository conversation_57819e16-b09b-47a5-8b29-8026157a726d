# SBCDP API v3 - 建造者模式架构

## 📁 模块结构

```
sbcdp/api_v3/
├── __init__.py          # 主入口，导出所有公共API
├── base.py              # 核心FluentCDPMethods类和工具方法
├── builders.py          # 所有构建器类
├── sync_wrapper.py      # 同步包装器
└── README.md           # 本文档
```

## 🏗️ 架构设计

### 1. **base.py** - 核心基础类
- `FluentCDPMethods`: 主要的API类
- 工具方法：`_slow_mode_pause_if_set`, `_add_light_pause`, `_convert_to_css_if_xpath`, `_add_element_methods`
- 向后兼容方法：所有原有的直接调用方法
- 建造者工厂方法：`navigate()`, `find()`, `wait()`, `page_info()`, `gui()`

### 2. **builders.py** - 构建器类集合
- `NavigationBuilder`: 导航操作（to, reload, back, forward）
- `ElementBuilder`: 元素操作（click, type, get_text, get_attribute等）
- `WaitBuilder`: 等待操作（for_element, for_text, assert_element等）
- `PageInfoBuilder`: 页面信息（title, url, source, evaluate等）
- `GUIBuilder`: GUI操作（click_at, hover_at, press_key等）

### 3. **sync_wrapper.py** - 同步包装器
- `SyncFluentCDPMethods`: 提供同步版本的API调用

### 4. **__init__.py** - 公共接口
- 导入并导出所有公共类
- 提供别名以保持向后兼容

## 🎯 使用方式

### 异步API（推荐）
```python
from sbcdp.api_v3 import AsyncCDPMethods

async def example():
    async with Chrome() as chrome:
        methods = AsyncCDPMethods(chrome.page, chrome.driver)
        
        # 建造者模式 - 流式调用
        await (methods
               .navigate().to("https://example.com")
               .find("#search").type("hello world")
               .find("#submit").click()
               .wait().for_text("Results"))
        
        # 向后兼容 - 直接调用
        await methods.get("https://example.com")
        await methods.type("#search", "hello world")
        await methods.click("#submit")
```

### 同步API
```python
from sbcdp.api_v3 import SyncCDPMethods

def example():
    with Chrome() as chrome:
        methods = SyncCDPMethods(chrome.loop, chrome.page, chrome.driver)
        
        # 同步调用
        methods.get("https://example.com")
        methods.type("#search", "hello world")
        methods.click("#submit")
```

## ✨ 设计优势

### 1. **模块化**
- 每个文件职责单一，易于维护
- 构建器类按功能分组，逻辑清晰
- 核心逻辑与构建器分离

### 2. **可扩展性**
- 新增构建器只需在builders.py中添加
- 新增方法只需在对应构建器中实现
- 不影响现有代码

### 3. **IDE友好**
- 完美的代码跳转和补全
- 清晰的类型提示
- 无需TYPE_CHECKING技巧

### 4. **向后兼容**
- 保持所有原有API不变
- 渐进式迁移策略
- 同时支持新旧两种调用方式

## 🔧 维护指南

### 添加新的构建器
1. 在`builders.py`中创建新的构建器类
2. 在`base.py`的`FluentCDPMethods`中添加工厂方法
3. 在`__init__.py`中导出新类

### 添加新的方法
1. 在对应的构建器类中添加方法
2. 如需向后兼容，在`base.py`中添加直接调用方法
3. 如需同步支持，在`sync_wrapper.py`中添加同步版本

### 修改核心逻辑
1. 工具方法修改在`base.py`中进行
2. 确保不破坏现有API契约
3. 运行测试确保兼容性

## 🧪 测试覆盖

当前通过的测试：
- ✅ `test_basic_navigation` - 基础导航功能
- ✅ `test_element_interaction` - 元素交互功能  
- ✅ `test_visibility_checks` - 可见性检查
- ✅ `test_concurrent_operations` - 并发操作
- ✅ `test_context_manager` - 上下文管理器

## 📈 性能特点

- **懒加载**: 元素只在需要时获取
- **链式调用**: 减少中间对象创建
- **异步优先**: 充分利用异步IO优势
- **内存友好**: 构建器对象轻量级
