"""
页面信息组件
包含页面信息获取、元素属性获取、存储操作等方法
"""

from typing import Any, Dict, Optional
from contextlib import suppress

from .base_component import BaseComponent


class PageInfoComponent(BaseComponent):
    """页面信息组件"""
    
    async def get_title(self) -> str:
        """获取页面标题"""
        return await self.page.evaluate("document.title")
    
    async def get_current_url(self) -> str:
        """获取当前URL"""
        return await self.page.evaluate("window.location.href")
    
    async def get_page_source(self) -> str:
        """获取页面源码"""
        return await self.page.evaluate("document.documentElement.outerHTML")
    
    async def get_window_rect(self) -> Dict[str, Any]:
        """获取窗口矩形信息"""
        return await self.page.evaluate("""
            () => {
                return {
                    x: window.screenX,
                    y: window.screenY,
                    width: window.outerWidth,
                    height: window.outerHeight,
                    innerWidth: window.innerWidth,
                    innerHeight: window.innerHeight,
                    pageXOffset: window.pageXOffset,
                    pageYOffset: window.pageYOffset,
                    scrollX: window.scrollX,
                    scrollY: window.scrollY
                };
            }
        """)
    
    async def get_element_rect(self, selector: str) -> Dict[str, Any]:
        """获取元素矩形信息"""
        element = await self.methods.dom.find_element(selector)
        return await element.get_position()
    
    async def get_element_attribute(self, selector: str, attribute: str) -> Optional[str]:
        """获取元素属性"""
        element = await self.methods.dom.find_element(selector)
        return await element.get_attribute(attribute)
    
    async def get_element_html(self, selector: str) -> str:
        """获取元素HTML"""
        element = await self.methods.dom.find_element(selector)
        return await element.get_html()
    
    async def evaluate(self, script: str) -> Any:
        """执行JavaScript代码"""
        return await self.page.evaluate(script)
    
    async def execute_script(self, script: str) -> Any:
        """执行JavaScript代码（evaluate的别名）"""
        return await self.evaluate(script)
    
    async def localStorage_get_item(self, key: str) -> Optional[str]:
        """获取localStorage项"""
        return await self.page.evaluate(f"localStorage.getItem('{key}')")
    
    async def localStorage_set_item(self, key: str, value: str) -> None:
        """设置localStorage项"""
        await self.page.evaluate(f"localStorage.setItem('{key}', '{value}')")
    
    async def localStorage_remove_item(self, key: str) -> None:
        """删除localStorage项"""
        await self.page.evaluate(f"localStorage.removeItem('{key}')")
    
    async def localStorage_clear(self) -> None:
        """清空localStorage"""
        await self.page.evaluate("localStorage.clear()")
    
    async def sessionStorage_get_item(self, key: str) -> Optional[str]:
        """获取sessionStorage项"""
        return await self.page.evaluate(f"sessionStorage.getItem('{key}')")
    
    async def sessionStorage_set_item(self, key: str, value: str) -> None:
        """设置sessionStorage项"""
        await self.page.evaluate(f"sessionStorage.setItem('{key}', '{value}')")
    
    async def sessionStorage_remove_item(self, key: str) -> None:
        """删除sessionStorage项"""
        await self.page.evaluate(f"sessionStorage.removeItem('{key}')")
    
    async def sessionStorage_clear(self) -> None:
        """清空sessionStorage"""
        await self.page.evaluate("sessionStorage.clear()")
