"""
SBCDP API v3 同步包装器
提供同步版本的API调用
"""

from typing import Optional
from .base import FluentCDPMethods


class SyncFluentCDPMethods:
    """同步流式CDP方法类"""

    def __init__(self, loop, page, driver):
        self.loop = loop
        self.async_methods = FluentCDPMethods(page, driver)
        self.doing = False

    def _run_sync(self, coro):
        """同步执行协程"""
        if not self.doing:
            try:
                self.doing = True
                return self.loop.run_until_complete(coro)
            finally:
                self.doing = False
        return coro

    # 向后兼容的同步方法
    def get(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs):
        return self._run_sync(self.async_methods.get(url, new_tab=new_tab, new_window=new_window, **kwargs))

    def find_element(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.find_element(selector, timeout=timeout))

    def click(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.click(selector, timeout=timeout))

    def type(self, selector: str, text: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.type(selector, text, timeout=timeout))

    def get_text(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.get_text(selector, timeout=timeout))

    def get_title(self):
        return self._run_sync(self.async_methods.get_title())

    def get_current_url(self):
        return self._run_sync(self.async_methods.get_current_url())

    def evaluate(self, script: str):
        return self._run_sync(self.async_methods.evaluate(script))

    def save_screenshot(self, filename: str = "screenshot.png"):
        return self._run_sync(self.async_methods.save_screenshot(filename))

    def find_elements(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.find_elements(selector, timeout=timeout))

    def get_attribute(self, selector: str, attribute: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.get_attribute(selector, attribute, timeout=timeout))

    def set_value(self, selector: str, value: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.set_value(selector, value, timeout=timeout))

    def is_element_present(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.is_element_present(selector, timeout=timeout))

    def is_element_visible(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.is_element_visible(selector, timeout=timeout))

    def get_page_source(self):
        return self._run_sync(self.async_methods.get_page_source())

    def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None):
        return self._run_sync(self.async_methods.reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load))

    def send_keys(self, selector: str, text: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.send_keys(selector, text, timeout=timeout))

    def clear(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.clear(selector, timeout=timeout))

    def scroll_to_top(self):
        return self._run_sync(self.async_methods.scroll_to_top())

    def scroll_to_bottom(self):
        return self._run_sync(self.async_methods.scroll_to_bottom())

    def scroll_into_view(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.scroll_into_view(selector, timeout=timeout))

    def flash(self, selector: str, timeout: Optional[float] = None):
        return self._run_sync(self.async_methods.flash(selector, timeout=timeout))
