"""
GUI操作组件
包含PyAutoGUI相关的所有GUI操作方法
"""

import asyncio
from typing import Optional

from .base_component import BaseComponent


class GUIComponent(BaseComponent):
    """GUI操作组件"""
    
    async def gui_click_x_y(self, x: float, y: float, timeframe: Optional[float] = None) -> None:
        """在指定坐标点击"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")
        
        if not timeframe:
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3
        
        # 移动到目标位置
        pyautogui.moveTo(x, y, duration=timeframe)
        await asyncio.sleep(0.1)
        
        # 点击
        pyautogui.click(x, y)
        await self._slow_mode_pause_if_set()
    
    async def gui_hover_x_y(self, x: float, y: float, timeframe: Optional[float] = None) -> None:
        """在指定坐标悬停"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")
        
        if not timeframe:
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3
        
        pyautogui.moveTo(x, y, duration=timeframe)
        await self._slow_mode_pause_if_set()
    
    async def gui_press_key(self, key: str) -> None:
        """按下指定按键"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")
        
        pyautogui.press(key)
        await self._slow_mode_pause_if_set()
    
    async def gui_write(self, text: str, interval: float = 0.0) -> None:
        """输入文本"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")
        
        pyautogui.write(text, interval=interval)
        await self._slow_mode_pause_if_set()
