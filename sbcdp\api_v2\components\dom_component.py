"""
DOM操作组件
包含元素查找、操作和状态检查相关方法
"""

import time
from contextlib import suppress
from typing import Any, Optional

from ...fixtures import page_utils
from ... import settings
from .base_component import BaseComponent


class DOMComponent(BaseComponent):
    """DOM操作组件"""
    
    async def find_element(self, selector: str, best_match: bool = False, timeout: Optional[float] = None) -> Any:
        """查找元素"""
        if not selector:
            raise ValueError("Selector cannot be empty")

        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        selector = self._convert_to_css_if_xpath(selector)
        early_failure = False
        if (":contains(") in selector:
            selector, _ = page_utils.recalculate_selector(
                selector, by="css selector", xp_ok=True
            )
        failure = False
        try:
            if early_failure:
                raise Exception("Failed!")
            element = await self.page.find(selector, best_match=best_match, timeout=timeout)
        except Exception:
            failure = True
            plural = "s"
            if timeout == 1:
                plural = ""
            message = "\n Element {%s} was not found after %s second%s!" % (
                selector,
                timeout,
                plural,
            )
        if failure:
            raise Exception(message)
        element = self._add_element_methods(element)
        await self._slow_mode_pause_if_set()
        return element
    
    async def select(self, selector: str, timeout: Optional[float] = None) -> Any:
        """选择元素（find_element的别名）"""
        return await self.find_element(selector, timeout=timeout)
    
    async def select_all(self, selector: str, timeout: Optional[float] = None) -> list:
        """选择所有匹配的元素"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        selector = self._convert_to_css_if_xpath(selector)
        
        elements = await self.page.find_elements(selector, timeout=timeout)
        updated_elements = []
        for element in elements:
            element = self._add_element_methods(element)
            updated_elements.append(element)
        await self._slow_mode_pause_if_set()
        return updated_elements
    
    async def find_element_by_text(self, text: str, tag_name: Optional[str] = None, timeout: Optional[float] = None) -> Any:
        """通过文本查找元素"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        time_now = time.time()
        
        # 使用wait组件的方法
        await self.methods.wait.assert_text(text, timeout=timeout)
        spent = int(time.time() - time_now)
        remaining = 1 + timeout - spent
        
        if tag_name:
            await self.methods.wait.assert_element(tag_name, timeout=remaining)
        
        elements = await self.page.find_elements_by_text(text=text)
        if tag_name:
            tag_name = tag_name.lower().strip()
        
        for element in elements:
            if element and not tag_name:
                element = self._add_element_methods(element)
                return element
            elif (
                element
                and tag_name in element.tag_name.lower()
                and text.strip() in element.text
            ):
                element = self._add_element_methods(element)
                return element
            elif (
                element
                and element.parent
                and tag_name in element.parent.tag_name.lower()
                and text.strip() in element.parent.text
            ):
                element = self._add_element_methods(element.parent)
                return element
            elif (
                element
                and element.parent
                and element.parent.parent
                and tag_name in element.parent.parent.tag_name.lower()
                and text.strip() in element.parent.parent.text
            ):
                element = self._add_element_methods(element.parent.parent)
                return element
        
        plural = "s"
        if timeout == 1:
            plural = ""
        message = f"\n Element with text '{text}' was not found after {timeout} second{plural}!"
        raise Exception(message)
    
    async def is_element_present(self, selector: str) -> bool:
        """检查元素是否存在"""
        try:
            await self.find_element(selector, timeout=0.1)
            return True
        except Exception:
            return False
    
    async def is_element_visible(self, selector: str) -> bool:
        """检查元素是否可见"""
        try:
            element = await self.find_element(selector, timeout=0.1)
            return element.is_displayed()
        except Exception:
            return False
    
    async def get_text(self, selector: str) -> str:
        """获取元素文本"""
        element = await self.find_element(selector)
        return await element.text_content()
    
    async def get_attribute(self, selector: str, attribute: str) -> str:
        """获取元素属性"""
        element = await self.find_element(selector)
        return await element.get_attribute(attribute)
