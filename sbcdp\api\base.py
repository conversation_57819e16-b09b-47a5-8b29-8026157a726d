"""
SBCDP API基础类
提供所有API模块的共同基础功能
"""

import asyncio
import sys
from contextlib import suppress
from typing import Optional

from .. import settings
from ..fixtures import page_utils, js_utils, shared_utils, constants


class BaseAPIMethods:
    """API方法基础类"""
    
    def __init__(self, page, driver):
        self.page = page
        self.driver = driver
    
    async def _slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        if (
            (hasattr(self.driver.config, "demo_mode") and self.driver.config.demo_mode)
            or "--demo" in sys.argv
        ):
            await asyncio.sleep(0.48)
        elif (
            (hasattr(self.driver.config, "slow_mode") and self.driver.config.slow_mode)
            or "--slow" in sys.argv
        ):
            await asyncio.sleep(0.24)

    async def _add_light_pause(self):
        """添加轻微暂停"""
        await asyncio.sleep(0.007)

    def _convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        if page_utils.is_xpath_selector(selector):
            with suppress(Exception):
                css = js_utils.convert_to_css_selector(selector, "xpath")
                if css:
                    return css
        return selector

    def _add_element_methods(self, element):
        """为元素添加额外的方法"""
        if not element:
            return element
        
        # 检查是否已经添加过方法
        if hasattr(element, '_methods_added'):
            return element
        
        # 添加text_all属性
        if not hasattr(element, 'text_all'):
            async def get_text_all():
                try:
                    return element.text or ""
                except Exception:
                    return ""
            element.text_all = get_text_all
        
        # 标记已添加方法
        element._methods_added = True
        
        return element
