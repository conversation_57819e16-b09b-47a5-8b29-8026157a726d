"""
SBCDP API基础类
提供所有API模块的共同基础功能
"""

import asyncio
import sys
from contextlib import suppress
from weakref import ref

from ..fixtures import page_utils, js_utils


class Base:
    """API方法基础类"""
    
    def __init__(self, cdp):
        self.__cdp = ref(cdp)

    @property
    def cdp(self):
        return self.__cdp()

    async def _slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        if (
            (hasattr(self.cdp.driver.config, "demo_mode") and self.cdp.driver.config.demo_mode)
            or "--demo" in sys.argv
        ):
            await asyncio.sleep(0.48)
        elif (
            (hasattr(self.cdp.driver.config, "slow_mode") and self.cdp.driver.config.slow_mode)
            or "--slow" in sys.argv
        ):
            await asyncio.sleep(0.24)

    async def _add_light_pause(self):
        """添加轻微暂停"""
        await asyncio.sleep(0.007)

    def _convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        if page_utils.is_xpath_selector(selector):
            with suppress(Exception):
                css = js_utils.convert_to_css_selector(selector, "xpath")
                if css:
                    return css
        return selector

