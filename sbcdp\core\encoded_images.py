"""
简化的encoded_images模块，用于CDP-Base框架
只提供基本的占位符功能，移除了所有实际的图像编码
"""

# 占位符变量
DASH_PIE_PNG_1 = ""
DASH_PIE_PNG_2 = ""
DASH_PIE_PNG_3 = ""
REPORT_FAVICON = ""
SIDE_BY_SIDE_PNG = ""


def get_dash_pie_png1():
    """返回空字符串占位符"""
    return ""


def get_dash_pie_png2():
    """返回空字符串占位符"""
    return ""


def get_dash_pie_png3():
    """返回空字符串占位符"""
    return ""


def get_report_favicon():
    """返回空字符串占位符"""
    return ""


def get_side_by_side_png():
    """返回空字符串占位符"""
    return ""
