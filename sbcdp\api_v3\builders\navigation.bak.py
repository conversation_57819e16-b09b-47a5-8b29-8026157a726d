"""
SBCDP API v3 导航构建器
处理页面导航相关操作
"""

import asyncio
from typing import Optional

from ... import settings
from ...fixtures import shared_utils, constants


class NavigationBuilder:
    """导航操作构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def to(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs):
        """导航到URL"""
        url = shared_utils.fix_url_as_needed(url)
        await self.methods.page.get(url, new_tab=new_tab, new_window=new_window, **kwargs)
        url_protocol = url.split(":")[0]
        safe_url = True
        if url_protocol not in ["about", "data", "chrome"]:
            safe_url = False
        if not safe_url:
            await asyncio.sleep(constants.UC.CDP_MODE_OPEN_WAIT)
            if shared_utils.is_windows():
                await asyncio.sleep(constants.UC.EXTRA_WINDOWS_WAIT)
        else:
            await asyncio.sleep(0.012)
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def open(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs):
        return await self.to(url, new_tab=new_tab, new_window=new_window, **kwargs)

    async def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None):
        """重新加载页面"""
        await self.methods.page.reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load)
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def refresh(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None):
        return await self.reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load)

    async def back(self):
        """后退"""
        await self.methods.page.go_back()
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def forward(self):
        """前进"""
        await self.methods.page.go_forward()
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def get_navigation_history(self):
        return await self.methods.page.get_navigation_history()
