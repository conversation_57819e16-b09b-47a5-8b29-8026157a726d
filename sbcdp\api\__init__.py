"""
SBCDP API模块
使用组合模式 + 代理模式重构AsyncCDPMethods
"""

from .navigation import NavigationMethods
from .element import ElementMethods
from .wait import WaitMethods
from .page_info import PageInfoMethods
from .gui import GUIMethods
from .action import ActionMethods

__all__ = [
    'NavigationMethods',
    'ElementMethods', 
    'WaitMethods',
    'PageInfoMethods',
    'GUIMethods',
    'ActionMethods'
]
