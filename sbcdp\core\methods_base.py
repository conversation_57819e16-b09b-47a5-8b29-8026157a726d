"""
SBCDP 异步方法基础类
包含基础初始化、工具方法和元素方法增强
"""

import asyncio
import sys
from contextlib import suppress

from ..fixtures import page_utils
from ..fixtures import js_utils


class AsyncCDPBase:
    """异步CDP方法基础类"""

    def __init__(self, page, driver):
        self.page = page
        self.driver = driver

    async def __slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        if (
            (hasattr(self.driver.config, "demo_mode") and self.driver.config.demo_mode)
            or "--demo" in sys.argv
        ):
            await asyncio.sleep(0.48)
        elif (
            (hasattr(self.driver.config, "slow_mode") and self.driver.config.slow_mode)
            or "--slow" in sys.argv
        ):
            await asyncio.sleep(0.24)

    async def __add_light_pause(self):
        """添加轻微暂停"""
        await asyncio.sleep(0.007)

    def __convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        if page_utils.is_xpath_selector(selector):
            with suppress(Exception):
                css = js_utils.convert_to_css_selector(selector, "xpath")
                if css:
                    return css
        return selector

    def __add_element_methods(self, element):
        """为元素添加方法"""
        if not element:
            return element

        # 基础操作方法
        element.clear_input = lambda: self.__clear_input(element)
        element.click = lambda: self.__click(element)
        element.flash = (
            lambda *args, **kwargs: self.__flash(element, *args, **kwargs)
        )
        element.focus = lambda: self.__focus(element)
        element.gui_click = (
            lambda *args, **kwargs: self.__gui_click(element, *args, **kwargs)
        )
        element.highlight_overlay = lambda: self.__highlight_overlay(element)
        element.mouse_click = lambda: self.__mouse_click(element)
        element.mouse_drag = lambda destination: self.__mouse_drag(element, destination)
        element.mouse_move = lambda: self.__mouse_move(element)
        element.press_keys = lambda text: self.__press_keys(element, text)

        # 查询方法
        element.query_selector = lambda selector: self.__query_selector(element, selector)
        element.querySelector = element.query_selector
        element.query_selector_all = lambda selector: self.__query_selector_all(element, selector)
        element.querySelectorAll = element.query_selector_all

        # DOM操作方法
        element.remove_from_dom = lambda: self.__remove_from_dom(element)
        element.save_screenshot = (
            lambda *args, **kwargs: self.__save_screenshot(element, *args, **kwargs)
        )
        element.save_to_dom = lambda: self.__save_to_dom(element)
        element.scroll_into_view = lambda: self.__scroll_into_view(element)
        element.select_option = lambda: self.__select_option(element)

        # 输入方法
        element.send_file = lambda *file_paths: self.__send_file(element, *file_paths)
        element.send_keys = lambda text: self.__send_keys(element, text)
        element.set_text = lambda value: self.__set_text(element, value)
        element.set_value = lambda value: self.__set_value(element, value)
        element.type = lambda text: self.__type(element, text)

        # 获取信息方法
        element.get_position = lambda: self.__get_position(element)
        element.get_html = lambda: self.__get_html(element)
        element.get_js_attributes = lambda: self.__get_js_attributes(element)
        element.get_attribute = lambda attribute: self.__get_attribute(element, attribute)

        # Shadow Root方法
        element.sr_query_selector = (
            lambda selector: self.__shadow_root_query_selector(element, selector)
        )
        element.shadow_root_query_selector = element.sr_query_selector
        element.sr_query_selector_all = (
            lambda selector: self.__shadow_root_query_selector_all(element, selector)
        )
        element.shadow_root_query_selector_all = element.sr_query_selector_all

        # 父元素获取（应该放在最后）
        element.get_parent = lambda: self.__get_parent(element)

        return element

    # Shadow Root相关方法
    async def __shadow_root_query_selector(self, element, selector: str):
        selector = self.__convert_to_css_if_xpath(selector)
        element2 = await element.shadow_root_query_selector_async(selector)
        element2 = self.__add_element_methods(element2)
        return element2

    async def __shadow_root_query_selector_all(self, element, selector):
        selector = self.__convert_to_css_if_xpath(selector)
        elements = await element.shadow_root_query_selector_all_async(selector)
        updated_elements = []
        for element in elements:
            element = self.__add_element_methods(element)
            updated_elements.append(element)
        await self.__slow_mode_pause_if_set()
        return updated_elements

    # 基础元素操作方法
    async def __clear_input(self, element):
        return await element.clear_input_async()

    async def __click(self, element):
        result = await element.click_async()
        await self.page.wait()
        return result

    async def __focus(self, element):
        return await element.focus_async()

    async def __highlight_overlay(self, element):
        return await element.highlight_overlay_async()

    async def __mouse_click(self, element):
        result = await element.mouse_click_async()
        await self.page.wait()
        return result

    async def __mouse_drag(self, element, destination):
        return await element.mouse_drag_async(destination)

    async def __mouse_move(self, element):
        return await element.mouse_move_async()

    async def __query_selector(self, element, selector):
        selector = self.__convert_to_css_if_xpath(selector)
        element2 = await element.query_selector_async(selector)
        element2 = self.__add_element_methods(element2)
        return element2

    async def __query_selector_all(self, element, selector):
        selector = self.__convert_to_css_if_xpath(selector)
        elements = await element.query_selector_all_async(selector)
        updated_elements = []
        for element in elements:
            element = self.__add_element_methods(element)
            updated_elements.append(element)
        await self.__slow_mode_pause_if_set()
        return updated_elements

    async def __remove_from_dom(self, element):
        return await element.remove_from_dom_async()

    async def __save_screenshot(self, element, *args, **kwargs):
        return await element.save_screenshot_async(*args, **kwargs)

    async def __save_to_dom(self, element):
        return await element.save_to_dom_async()

    async def __scroll_into_view(self, element):
        await element.scroll_into_view_async()
        await self.__add_light_pause()
        return None

    async def __select_option(self, element):
        return await element.select_option_async()

    async def __send_file(self, element, *file_paths):
        return await element.send_file_async(*file_paths)

    async def __send_keys(self, element, text):
        return await element.send_keys_async(text)

    async def __set_text(self, element, value):
        return await element.set_text_async(value)

    async def __set_value(self, element, value):
        return await element.set_value_async(value)

    async def __type(self, element, text):
        with suppress(Exception):
            await element.clear_input()
        await element.send_keys(text)

    async def __get_position(self, element):
        return await element.get_position_async()

    async def __get_html(self, element):
        return await element.get_html_async()

    async def __get_js_attributes(self, element):
        return await element.get_js_attributes_async()

    async def __get_attribute(self, element, attribute):
        try:
            return (await element.get_js_attributes())[attribute]
        except Exception:
            if not attribute:
                raise
            try:
                attribute_str = await element.get_js_attributes()
                locate = ' %s="' % attribute
                if locate in attribute_str.outerHTML:
                    outer_html = attribute_str.outerHTML
                    attr_start = outer_html.find(locate) + len(locate)
                    attr_end = outer_html.find('"', attr_start)
                    value = outer_html[attr_start:attr_end]
                    return value
            except Exception:
                pass
        return None

    async def __get_parent(self, element):
        return self.__add_element_methods(element.parent)

    # 滚动偏移获取方法
    async def __get_x_scroll_offset(self):
        x_scroll_offset = await self.page.evaluate("window.pageXOffset")
        return x_scroll_offset or 0

    async def __get_y_scroll_offset(self):
        y_scroll_offset = await self.page.evaluate("window.pageYOffset")
        return y_scroll_offset or 0

    async def __flash(self, element, *args, **kwargs):
        await element.scroll_into_view()
        if len(args) < 3 and "x_offset" not in kwargs:
            x_offset = await self.__get_x_scroll_offset()
            kwargs["x_offset"] = x_offset
        if len(args) < 3 and "y_offset" not in kwargs:
            y_offset = await self.__get_y_scroll_offset()
            kwargs["y_offset"] = y_offset
        return await element.flash_async(*args, **kwargs)

    async def __gui_click(self, element, timeframe=None):
        element.scroll_into_view()
        await self.__add_light_pause()
        position = element.get_position()
        x = position.x
        y = position.y
        e_width = position.width
        e_height = position.height
        # Relative to window
        element_rect = {"height": e_height, "width": e_width, "x": x, "y": y}
        window_rect = await self.get_window_rect()
        w_bottom_y = window_rect["y"] + window_rect["height"]
        viewport_height = window_rect["innerHeight"]
        x = window_rect["x"] + element_rect["x"]
        y = w_bottom_y - viewport_height + element_rect["y"]
        y_scroll_offset = window_rect["pageYOffset"]
        y = y - y_scroll_offset
        x = x + window_rect["scrollX"]
        y = y + window_rect["scrollY"]
        # Relative to screen
        element_rect = {"height": e_height, "width": e_width, "x": x, "y": y}
        e_width = element_rect["width"]
        e_height = element_rect["height"]
        e_x = element_rect["x"]
        e_y = element_rect["y"]
        x, y = ((e_x + e_width / 2.0) + 0.5), ((e_y + e_height / 2.0) + 0.5)
        if not timeframe or not isinstance(timeframe, (int, float)):
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3
        await self.gui_click_x_y(x, y, timeframe=timeframe)
        return await self.page.wait()

    async def __press_keys(self, element, text):
        await element.scroll_into_view()
        submit = False
        if text.endswith("\n") or text.endswith("\r"):
            submit = True
            text = text[:-1]
        for key in text:
            await element.send_keys(key)
            await asyncio.sleep(0.044)
        if submit:
            await element.send_keys("\r\n")
            await asyncio.sleep(0.044)
        await self.__slow_mode_pause_if_set()
        return await self.page.sleep(0.025)
