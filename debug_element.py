#!/usr/bin/env python3
"""
调试元素对象的方法
"""

import asyncio
from sbcdp import Chrome

async def debug_element():
    """调试元素对象"""
    async with Chrome() as chrome:
        # 使用和测试相同的HTML
        html = """
        <html>
        <head><title>Async Element Test</title></head>
        <body>
            <h1 id="title">Async Test Title</h1>
            <input id="input1" type="text" placeholder="Enter text">
            <button id="btn1" onclick="document.getElementById('result').innerText='Async Clicked'">Click Me</button>
            <div id="result"></div>
        </body>
        </html>
        """
        await chrome.get(f"data:text/html,{html}")

        print("=== 输入前 ===")
        element = await chrome.find_element("#input1")
        print("Element text:", element.text)
        print("Element attrs:", element.attrs)
        value_before = await chrome.get_attribute("#input1", "value")
        print("get_attribute('value') before:", repr(value_before))

        print("\n=== 执行type操作 ===")
        await chrome.type("#input1", "Hello Async World")

        print("\n=== 输入后 ===")
        element = await chrome.find_element("#input1")
        print("Element text:", element.text)
        print("Element attrs:", element.attrs)
        value_after = await chrome.get_attribute("#input1", "value")
        print("get_attribute('value') after:", repr(value_after))

        # 测试直接调用元素方法
        print("\n=== 测试直接调用元素方法 ===")
        print("Element has clear_input_async:", hasattr(element, 'clear_input_async'))
        print("Element has send_keys_async:", hasattr(element, 'send_keys_async'))
        print("Element has clear_input:", hasattr(element, 'clear_input'))
        print("Element has send_keys:", hasattr(element, 'send_keys'))

        if hasattr(element, 'clear_input'):
            print("clear_input type:", type(element.clear_input))
        if hasattr(element, 'send_keys'):
            print("send_keys type:", type(element.send_keys))

        try:
            print("Trying clear_input_async...")
            await element.clear_input_async()
            print("Trying send_keys_async...")
            await element.send_keys_async("Direct Test")

            # 等待一下让DOM更新
            import asyncio
            await asyncio.sleep(0.1)

            # 重新获取元素
            element2 = await chrome.find_element("#input1")
            print("After direct call - Element text:", element2.text)
            value_direct = await chrome.get_attribute("#input1", "value")
            print("get_attribute('value') after direct:", repr(value_direct))

            # 尝试使用JavaScript获取值
            js_value = await chrome.evaluate("document.getElementById('input1').value")
            print("JavaScript value:", repr(js_value))

        except Exception as e:
            print("Direct call error:", e)

if __name__ == "__main__":
    asyncio.run(debug_element())
