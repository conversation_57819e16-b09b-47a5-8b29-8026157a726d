#!/usr/bin/env python3
"""
调试元素对象的方法
"""

import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

# 使用原始API
from sbcdp.chrome import Chrome as OriginalChrome

async def debug_element():
    """调试元素对象"""
    # 临时修改导入以使用原始API
    from sbcdp.chrome import Chrome
    chrome = Chrome()
    await chrome.get("data:text/html,<html><body><input id='test' type='text'></body></html>")

    # 获取元素
    element = await chrome.find_element("#test")

    print("Element type:", type(element))
    print("Element attributes:")
    for attr in dir(element):
        if not attr.startswith('_'):
            value = getattr(element, attr)
            print(f"  {attr}: {type(value)} = {value}")

    await chrome.quit()

if __name__ == "__main__":
    asyncio.run(debug_element())
