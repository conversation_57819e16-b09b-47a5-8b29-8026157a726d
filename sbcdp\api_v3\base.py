"""
SBCDP API v3 基础类
包含主要的FluentCDPMethods类和工具方法
"""

import asyncio
import sys
from contextlib import suppress
from typing import Optional, Any, List, Union

from .. import settings
from ..fixtures import shared_utils, constants, page_utils, js_utils


class FluentCDPMethods:
    """流式CDP方法类"""

    def __init__(self, page, driver):
        self.page = page
        self.driver = driver
        self._current_element = None

    # === 基础工具方法 ===
    async def _slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        if (
            (hasattr(self.driver.config, "demo_mode") and self.driver.config.demo_mode)
            or "--demo" in sys.argv
        ):
            await asyncio.sleep(0.48)
        elif (
            (hasattr(self.driver.config, "slow_mode") and self.driver.config.slow_mode)
            or "--slow" in sys.argv
        ):
            await asyncio.sleep(0.24)

    async def _add_light_pause(self):
        """添加轻微暂停"""
        await asyncio.sleep(0.007)

    def _convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        if page_utils.is_xpath_selector(selector):
            with suppress(Exception):
                css = js_utils.convert_to_css_selector(selector, "xpath")
                if css:
                    return css
        return selector

    def _add_element_methods(self, element):
        """为元素添加额外的方法"""
        if not element:
            return element

        # 检查是否已经添加过方法
        if hasattr(element, '_methods_added'):
            return element

        # 添加基础操作方法（确保返回协程）
        element.clear_input = lambda: element.clear_input_async()
        element.click = lambda: element.click_async()
        element.send_keys = lambda text: element.send_keys_async(text)
        element.scroll_into_view = lambda: element.scroll_into_view_async()
        element.flash = lambda: element.flash_async()
        element.set_value = lambda value: element.set_value_async(value)
        element.set_text = lambda value: element.set_text_async(value)

        # 添加text_all属性
        if not hasattr(element, 'text_all'):
            async def get_text_all():
                try:
                    return element.text or ""
                except Exception:
                    return ""
            element.text_all = get_text_all

        # 标记已添加方法
        element._methods_added = True

        return element

    # === 向后兼容的直接方法 ===
    async def get(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs):
        """导航到URL（向后兼容）"""
        return await self.navigate().to(url, new_tab=new_tab, new_window=new_window, **kwargs)

    async def find_element(self, selector: str, timeout: Optional[float] = None):
        """查找元素（向后兼容）"""
        return await self.find(selector).get_element(timeout=timeout)

    async def click(self, selector: str, timeout: Optional[float] = None):
        """点击元素（向后兼容）"""
        await self.find(selector).click(timeout=timeout)
        return self

    async def type(self, selector: str, text: str, timeout: Optional[float] = None):
        """输入文本（向后兼容）"""
        await self.find(selector).type(text, timeout=timeout)
        return self

    async def get_text(self, selector: str, timeout: Optional[float] = None):
        """获取文本（向后兼容）"""
        return await self.find(selector).get_text(timeout=timeout)

    async def get_attribute(self, selector: str, attribute: str, timeout: Optional[float] = None):
        """获取属性（向后兼容）"""
        return await self.find(selector).get_attribute(attribute, timeout=timeout)

    async def get_title(self):
        """获取页面标题（向后兼容）"""
        return await self.page_info().title()

    async def get_current_url(self):
        """获取当前URL（向后兼容）"""
        return await self.page_info().url()

    async def evaluate(self, script: str):
        """执行JavaScript（向后兼容）"""
        return await self.page_info().evaluate(script)

    async def set_value(self, selector: str, value: str, timeout: Optional[float] = None):
        """设置值（向后兼容）"""
        await self.find(selector).set_value(value, timeout=timeout)
        return self

    async def is_element_present(self, selector: str, timeout: Optional[float] = None):
        """检查元素是否存在（向后兼容）"""
        return await self.find(selector).is_present(timeout=timeout)

    async def save_screenshot(self, filename: str = "screenshot.png"):
        """保存截图（向后兼容）"""
        return await self.page_info().save_screenshot(filename)

    async def find_elements(self, selector: str, timeout: Optional[float] = None):
        """查找多个元素（向后兼容）"""
        return await self.find(selector).get_all_elements(timeout=timeout)

    async def wait_for_element_visible(self, selector: str, timeout: Optional[float] = None):
        """等待元素可见（向后兼容）"""
        return await self.wait().for_element_visible(selector, timeout=timeout)

    async def assert_element(self, selector: str, timeout: Optional[float] = None):
        """断言元素存在（向后兼容）"""
        await self.wait().assert_element(selector, timeout=timeout)
        return self

    async def assert_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None):
        """断言文本存在（向后兼容）"""
        await self.wait().assert_text(text, selector=selector, timeout=timeout)
        return self

    async def scroll_to_top(self):
        """滚动到页面顶部（向后兼容）"""
        await self.page.evaluate("window.scrollTo(0, 0)")
        await self._add_light_pause()
        return self

    async def scroll_to_bottom(self):
        """滚动到页面底部（向后兼容）"""
        await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await self._add_light_pause()
        return self

    async def scroll_into_view(self, selector: str, timeout: Optional[float] = None):
        """滚动元素到可视区域（向后兼容）"""
        await self.find(selector).scroll_into_view(timeout=timeout)
        return self

    async def flash(self, selector: str, timeout: Optional[float] = None):
        """闪烁高亮元素（向后兼容）"""
        await self.find(selector).flash(timeout=timeout)
        return self

    async def send_keys(self, selector: str, text: str, timeout: Optional[float] = None):
        """发送按键（向后兼容）"""
        await self.find(selector).send_keys(text, timeout=timeout)
        return self

    async def clear(self, selector: str, timeout: Optional[float] = None):
        """清空元素（向后兼容）"""
        await self.find(selector).clear(timeout=timeout)
        return self

    async def is_element_visible(self, selector: str, timeout: Optional[float] = None):
        """检查元素是否可见（向后兼容）"""
        return await self.find(selector).is_visible(timeout=timeout)

    async def get_page_source(self):
        """获取页面源码（向后兼容）"""
        return await self.page_info().source()

    async def get_window_rect(self):
        """获取窗口矩形信息（向后兼容）"""
        return await self.page_info().get_window_rect()

    async def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None):
        """重新加载页面（向后兼容）"""
        await self.navigate().reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load)
        return self

    async def go_back(self):
        """后退（向后兼容）"""
        await self.navigate().back()
        return self

    async def go_forward(self):
        """前进（向后兼容）"""
        await self.navigate().forward()
        return self

    # === 建造者方法 ===
    def navigate(self):
        """返回导航操作构建器"""
        from .builders import NavigationBuilder
        return NavigationBuilder(self)

    def find(self, selector: str):
        """查找元素并返回元素操作构建器"""
        from .builders import ElementBuilder
        return ElementBuilder(self, selector)

    def select(self, selector: str):
        """选择元素（find的别名）"""
        return self.find(selector)

    def wait(self):
        """返回等待操作构建器"""
        from .builders import WaitBuilder
        return WaitBuilder(self)

    def page_info(self):
        """返回页面信息构建器"""
        from .builders import PageInfoBuilder
        return PageInfoBuilder(self)

    def gui(self):
        """返回GUI操作构建器"""
        from .builders import GUIBuilder
        return GUIBuilder(self)
