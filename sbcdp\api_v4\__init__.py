"""
SBCDP API v4 - 使用策略模式 + 工厂模式
提供最大的灵活性和可扩展性
"""

from abc import ABC, abstractmethod
from typing import Dict, Type, Any, Optional
import asyncio


class OperationStrategy(ABC):
    """操作策略基类"""
    
    def __init__(self, context):
        self.context = context
        self.page = context.page
        self.driver = context.driver
    
    @abstractmethod
    async def execute(self, *args, **kwargs):
        """执行操作"""
        pass


class CDPMethodsContext:
    """CDP方法上下文"""
    
    def __init__(self, page, driver):
        self.page = page
        self.driver = driver
        self._strategies: Dict[str, Type[OperationStrategy]] = {}
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """注册默认策略"""
        self.register_strategy("find_element", FindElementStrategy)
        self.register_strategy("click", ClickStrategy)
        self.register_strategy("type", TypeStrategy)
        self.register_strategy("navigate", NavigateStrategy)
        self.register_strategy("wait_for_element", WaitForElementStrategy)
        self.register_strategy("get_title", GetTitleStrategy)
        self.register_strategy("screenshot", ScreenshotStrategy)
    
    def register_strategy(self, name: str, strategy_class: Type[OperationStrategy]):
        """注册策略"""
        self._strategies[name] = strategy_class
    
    async def execute(self, operation: str, *args, **kwargs):
        """执行操作"""
        if operation not in self._strategies:
            raise ValueError(f"Unknown operation: {operation}")
        
        strategy = self._strategies[operation](self)
        return await strategy.execute(*args, **kwargs)
    
    # 便捷方法
    async def find_element(self, selector: str, **kwargs):
        return await self.execute("find_element", selector, **kwargs)
    
    async def click(self, selector: str, **kwargs):
        return await self.execute("click", selector, **kwargs)
    
    async def type(self, selector: str, text: str, **kwargs):
        return await self.execute("type", selector, text, **kwargs)
    
    async def navigate(self, url: str, **kwargs):
        return await self.execute("navigate", url, **kwargs)
    
    async def wait_for_element(self, selector: str, **kwargs):
        return await self.execute("wait_for_element", selector, **kwargs)
    
    async def get_title(self, **kwargs):
        return await self.execute("get_title", **kwargs)
    
    async def screenshot(self, filename: str = "screenshot.png", **kwargs):
        return await self.execute("screenshot", filename, **kwargs)


# 具体策略实现
class FindElementStrategy(OperationStrategy):
    """查找元素策略"""
    
    async def execute(self, selector: str, timeout: Optional[float] = None, **kwargs):
        if not timeout:
            timeout = 10.0
        return await self.page.find(selector, timeout=timeout)


class ClickStrategy(OperationStrategy):
    """点击策略"""
    
    async def execute(self, selector: str, timeout: Optional[float] = None, **kwargs):
        element = await self.context.find_element(selector, timeout=timeout)
        await element.click()
        return element


class TypeStrategy(OperationStrategy):
    """输入策略"""
    
    async def execute(self, selector: str, text: str, timeout: Optional[float] = None, **kwargs):
        element = await self.context.find_element(selector, timeout=timeout)
        await element.clear()
        await element.send_keys(text)
        return element


class NavigateStrategy(OperationStrategy):
    """导航策略"""
    
    async def execute(self, url: str, new_tab: bool = False, **kwargs):
        await self.page.get(url, new_tab=new_tab)
        return self.page


class WaitForElementStrategy(OperationStrategy):
    """等待元素策略"""
    
    async def execute(self, selector: str, timeout: Optional[float] = None, **kwargs):
        if not timeout:
            timeout = 30.0
        
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                element = await self.page.find(selector, timeout=0.5)
                if element.is_displayed():
                    return element
            except Exception:
                pass
            await asyncio.sleep(0.1)
        
        raise TimeoutError(f"Element {selector} not found within {timeout} seconds")


class GetTitleStrategy(OperationStrategy):
    """获取标题策略"""
    
    async def execute(self, **kwargs):
        return await self.page.evaluate("document.title")


class ScreenshotStrategy(OperationStrategy):
    """截图策略"""
    
    async def execute(self, filename: str = "screenshot.png", **kwargs):
        screenshot_data = await self.page.screenshot()
        with open(filename, 'wb') as f:
            f.write(screenshot_data)
        return filename


# 工厂类
class CDPMethodsFactory:
    """CDP方法工厂"""
    
    @staticmethod
    def create_async_methods(page, driver):
        """创建异步方法实例"""
        return CDPMethodsContext(page, driver)
    
    @staticmethod
    def create_sync_methods(loop, page, driver):
        """创建同步方法实例"""
        return SyncCDPMethodsWrapper(loop, page, driver)
    
    @staticmethod
    def create_custom_methods(page, driver, custom_strategies: Dict[str, Type[OperationStrategy]] = None):
        """创建自定义方法实例"""
        context = CDPMethodsContext(page, driver)
        if custom_strategies:
            for name, strategy in custom_strategies.items():
                context.register_strategy(name, strategy)
        return context


class SyncCDPMethodsWrapper:
    """同步方法包装器"""
    
    def __init__(self, loop, page, driver):
        self.loop = loop
        self.async_methods = CDPMethodsContext(page, driver)
    
    def find_element(self, selector: str, **kwargs):
        return self.loop.run_until_complete(self.async_methods.find_element(selector, **kwargs))
    
    def click(self, selector: str, **kwargs):
        return self.loop.run_until_complete(self.async_methods.click(selector, **kwargs))
    
    def type(self, selector: str, text: str, **kwargs):
        return self.loop.run_until_complete(self.async_methods.type(selector, text, **kwargs))
    
    def navigate(self, url: str, **kwargs):
        return self.loop.run_until_complete(self.async_methods.navigate(url, **kwargs))
    
    def wait_for_element(self, selector: str, **kwargs):
        return self.loop.run_until_complete(self.async_methods.wait_for_element(selector, **kwargs))
    
    def get_title(self, **kwargs):
        return self.loop.run_until_complete(self.async_methods.get_title(**kwargs))
    
    def screenshot(self, filename: str = "screenshot.png", **kwargs):
        return self.loop.run_until_complete(self.async_methods.screenshot(filename, **kwargs))


# 使用示例
async def strategy_example():
    """策略模式使用示例"""
    page = None  # 实际使用时从Chrome实例获取
    driver = None
    
    # 使用工厂创建方法实例
    methods = CDPMethodsFactory.create_async_methods(page, driver)
    
    # 基本操作
    await methods.navigate("https://example.com")
    await methods.click("button")
    title = await methods.get_title()
    
    # 自定义策略
    class CustomClickStrategy(OperationStrategy):
        async def execute(self, selector: str, **kwargs):
            print(f"Custom clicking: {selector}")
            element = await self.context.find_element(selector)
            await element.click()
            return element
    
    # 注册自定义策略
    methods.register_strategy("custom_click", CustomClickStrategy)
    await methods.execute("custom_click", "button")
    
    return methods
