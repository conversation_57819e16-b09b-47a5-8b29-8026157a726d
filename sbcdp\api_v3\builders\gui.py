"""
SBCDP API v3 GUI构建器
处理GUI自动化操作（需要pyautogui）
"""

import asyncio
from typing import Optional


class GUIBuilder:
    """GUI操作构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def click_at(self, x: float, y: float, timeframe: Optional[float] = None):
        """在坐标点击"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        if not timeframe:
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3

        # 移动到目标位置
        pyautogui.moveTo(x, y, duration=timeframe)
        await asyncio.sleep(0.1)

        # 点击
        pyautogui.click(x, y)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def hover_at(self, x: float, y: float, timeframe: Optional[float] = None):
        """在坐标悬停"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        if not timeframe:
            timeframe = 0.25
        if timeframe > 3:
            timeframe = 3

        pyautogui.moveTo(x, y, duration=timeframe)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def press_key(self, key: str):
        """按键"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        pyautogui.press(key)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def write(self, text: str, interval: float = 0.0):
        """输入文本"""
        try:
            import pyautogui
        except ImportError:
            raise ImportError("PyAutoGUI is required for GUI operations. Install with: pip install pyautogui")

        pyautogui.write(text, interval=interval)
        await self.methods._slow_mode_pause_if_set()
        return self.methods
