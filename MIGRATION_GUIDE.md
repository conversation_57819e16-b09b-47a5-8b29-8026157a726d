# SBCDP API 迁移指南

## 从多重继承迁移到组合模式

### 迁移概述

我们提供了一个更符合开发习惯的API设计，使用组合模式替代多重继承，完美解决IDE跳转问题。

### 新旧API对比

#### 旧API（多重继承）
```python
from sbcdp.api import AsyncCDPMethods

methods = AsyncCDPMethods(page, driver)
await methods.find_element("div")  # 跳转困难
await methods.click("button")      # 跳转困难
await methods.get("https://example.com")  # 跳转困难
```

#### 新API（组合模式）
```python
from sbcdp.api_v2 import AsyncCDPMethods

methods = AsyncCDPMethods(page, driver)

# 方式1: 向后兼容（推荐用于迁移期）
await methods.find_element("div")  # 完美跳转
await methods.click("button")      # 完美跳转
await methods.get("https://example.com")  # 完美跳转

# 方式2: 组件方式（推荐用于新代码）
await methods.dom.find_element("div")        # 跳转到 dom_component.py
await methods.action.click("button")         # 跳转到 action_component.py
await methods.navigation.get("https://example.com")  # 跳转到 navigation_component.py
```

### 迁移步骤

#### 步骤1：更新导入
```python
# 旧导入
from sbcdp.api import AsyncCDPMethods

# 新导入
from sbcdp.api_v2 import AsyncCDPMethods
```

#### 步骤2：选择使用方式

**保守迁移（推荐）：**
```python
# 保持原有代码不变，只更改导入
methods = AsyncCDPMethods(page, driver)
await methods.click("button")  # 原有代码无需修改
```

**渐进式迁移：**
```python
# 新代码使用组件方式
methods = AsyncCDPMethods(page, driver)
await methods.action.click("button")      # 新方式
await methods.click("button")             # 旧方式（仍然可用）
```

**完全迁移：**
```python
# 全部使用组件方式
methods = AsyncCDPMethods(page, driver)
dom = methods.dom
action = methods.action
nav = methods.navigation

element = await dom.find_element("input")
await action.type("input", "hello")
await action.click("button")
```

### API映射表

| 功能分类 | 旧API | 新API组件 | 示例 |
|---------|-------|-----------|------|
| 元素操作 | `methods.find_element()` | `methods.dom.find_element()` | 查找元素 |
| 页面动作 | `methods.click()` | `methods.action.click()` | 点击操作 |
| 导航操作 | `methods.get()` | `methods.navigation.get()` | 页面导航 |
| 等待操作 | `methods.wait_for_element_visible()` | `methods.wait.wait_for_element_visible()` | 等待元素 |
| GUI操作 | `methods.gui_click_x_y()` | `methods.gui.gui_click_x_y()` | GUI点击 |
| 页面信息 | `methods.get_title()` | `methods.page_info.get_title()` | 获取标题 |

### 组件详细说明

#### DOM组件 (`methods.dom`)
```python
# 元素查找
element = await methods.dom.find_element("div")
elements = await methods.dom.select_all(".item")
element = await methods.dom.find_element_by_text("Click me")

# 元素状态检查
is_present = await methods.dom.is_element_present("div")
is_visible = await methods.dom.is_element_visible("div")

# 元素信息获取
text = await methods.dom.get_text("div")
attr = await methods.dom.get_attribute("input", "value")
```

#### 动作组件 (`methods.action`)
```python
# 点击操作
await methods.action.click("button")
await methods.action.clear("input")

# 输入操作
await methods.action.type("input", "hello world")
await methods.action.send_keys("input", "text")

# 滚动操作
await methods.action.scroll_to_top()
await methods.action.scroll_to_bottom()
await methods.action.scroll_into_view("div")

# 截图和特效
await methods.action.save_screenshot("test.png")
await methods.action.flash("button")
```

#### 导航组件 (`methods.navigation`)
```python
# 页面导航
await methods.navigation.get("https://example.com")
await methods.navigation.open("https://example.com", new_tab=True)
await methods.navigation.reload()

# 历史导航
await methods.navigation.go_back()
await methods.navigation.go_forward()

# 标签页管理
await methods.navigation.switch_to_tab(1)
await methods.navigation.open_new_tab()
await methods.navigation.close_active_tab()
```

#### 等待组件 (`methods.wait`)
```python
# 等待元素
element = await methods.wait.wait_for_element_visible("div")
await methods.wait.wait_for_element_absent("loading")

# 断言操作
await methods.wait.assert_element("div")
await methods.wait.assert_text("Success", "div")
await methods.wait.assert_exact_text("Exact text", "span")

# 等待文本
await methods.wait.wait_for_text("Loading complete")
await methods.wait.sleep(2.0)
```

#### GUI组件 (`methods.gui`)
```python
# 坐标操作
await methods.gui.gui_click_x_y(100, 200)
await methods.gui.gui_hover_x_y(150, 250)

# 键盘操作
await methods.gui.gui_press_key("enter")
await methods.gui.gui_write("Hello World")
```

#### 页面信息组件 (`methods.page_info`)
```python
# 页面基本信息
title = await methods.page_info.get_title()
url = await methods.page_info.get_current_url()
source = await methods.page_info.get_page_source()

# 窗口信息
rect = await methods.page_info.get_window_rect()
element_rect = await methods.page_info.get_element_rect("div")

# JavaScript执行
result = await methods.page_info.evaluate("document.title")
result = await methods.page_info.execute_script("return window.location.href")

# 存储操作
await methods.page_info.localStorage_set_item("key", "value")
value = await methods.page_info.localStorage_get_item("key")
```

### 迁移最佳实践

#### 1. 渐进式迁移
```python
# 在同一个文件中混用新旧API
methods = AsyncCDPMethods(page, driver)

# 保持旧代码不变
await methods.click("old-button")

# 新代码使用组件方式
await methods.action.click("new-button")
```

#### 2. 创建便捷别名
```python
methods = AsyncCDPMethods(page, driver)

# 为常用组件创建别名
dom = methods.dom
action = methods.action
nav = methods.navigation

# 使用别名简化代码
element = await dom.find_element("div")
await action.click("button")
await nav.get("https://example.com")
```

#### 3. 团队协作
```python
# 在团队中统一使用方式
class TestBase:
    def setup_method(self):
        self.methods = AsyncCDPMethods(self.page, self.driver)
        # 统一使用组件方式
        self.dom = self.methods.dom
        self.action = self.methods.action
        self.nav = self.methods.navigation
    
    async def test_example(self):
        await self.nav.get("https://example.com")
        element = await self.dom.find_element("input")
        await self.action.type("input", "test")
```

### 常见问题

#### Q: 新API的性能如何？
A: 组合模式的性能开销极小，主要是多了一层方法调用，对实际使用几乎无影响。

#### Q: 可以混用新旧API吗？
A: 完全可以！新API保持了完全的向后兼容性。

#### Q: 如何处理现有的大量代码？
A: 建议渐进式迁移：
1. 先更改导入，保持代码不变
2. 新功能使用组件方式
3. 逐步重构关键模块

#### Q: IDE支持真的更好吗？
A: 是的！组件方式提供了完美的IDE支持：
- 精确的方法跳转
- 完整的代码补全
- 清晰的类型提示

### 总结

新的组合模式API提供了更好的开发体验，同时保持了完全的向后兼容性。建议采用渐进式迁移策略，逐步享受更好的IDE支持和代码组织方式。
