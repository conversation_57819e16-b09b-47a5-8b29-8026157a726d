#!/usr/bin/env python3
"""
SBCDP API v2 使用示例
展示组合模式的优势
"""

import asyncio
from sbcdp.api_v2 import AsyncCDPMethods


async def example_usage():
    """使用示例"""
    # 假设我们有page和driver对象
    page = None  # 实际使用时从Chrome实例获取
    driver = None  # 实际使用时从Chrome实例获取
    
    # 创建方法实例
    methods = AsyncCDPMethods(page, driver)
    
    # 方式1: 直接使用代理方法（向后兼容）
    await methods.get("https://example.com")
    await methods.click("button")
    title = await methods.get_title()
    
    # 方式2: 使用组件方法（更清晰的组织）
    await methods.navigation.get("https://example.com")
    await methods.action.click("button")
    title = await methods.page_info.get_title()
    
    # 方式3: 直接访问组件（最清晰）
    nav = methods.navigation
    action = methods.action
    dom = methods.dom
    
    await nav.get("https://example.com")
    element = await dom.find_element("input[name='search']")
    await action.type("input[name='search']", "hello world")
    await action.click("button[type='submit']")
    
    # IDE支持测试
    # 以下所有方法调用都应该能够正确跳转：
    
    # 跳转到 navigation_component.py
    await methods.navigation.get("https://example.com")
    
    # 跳转到 dom_component.py  
    element = await methods.dom.find_element("div")
    
    # 跳转到 action_component.py
    await methods.action.click("button")
    
    # 跳转到 wait_component.py
    await methods.wait.wait_for_element_visible("div")
    
    # 跳转到 gui_component.py
    await methods.gui.gui_click_x_y(100, 200)
    
    # 跳转到 page_info_component.py
    title = await methods.page_info.get_title()


def demo_ide_support():
    """演示IDE支持"""
    print("=== SBCDP API v2 IDE支持演示 ===")
    print()
    print("1. 完美的方法跳转:")
    print("   - methods.navigation.get() -> 跳转到 navigation_component.py")
    print("   - methods.dom.find_element() -> 跳转到 dom_component.py")
    print("   - methods.action.click() -> 跳转到 action_component.py")
    print()
    print("2. 清晰的代码组织:")
    print("   - 每个组件职责明确")
    print("   - 方法分类清晰")
    print("   - 易于维护和扩展")
    print()
    print("3. 向后兼容:")
    print("   - 保持原有API不变")
    print("   - 渐进式迁移")
    print("   - 新老代码可以共存")
    print()
    print("4. 开发体验:")
    print("   - 无需TYPE_CHECKING")
    print("   - 自然的代码补全")
    print("   - 符合Python开发习惯")


if __name__ == "__main__":
    demo_ide_support()
    # asyncio.run(example_usage())  # 需要真实的page和driver对象
