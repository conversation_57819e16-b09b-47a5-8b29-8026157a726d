"""
SBCDP API v3 等待构建器
处理各种等待和断言操作
"""

import asyncio
from typing import Optional

from ... import settings


class WaitBuilder:
    """等待操作构建器"""

    def __init__(self, methods):
        self.methods = methods

    async def for_element(self, selector: str, timeout: Optional[float] = None):
        """等待元素出现"""
        if timeout is None:
            timeout = settings.LARGE_TIMEOUT

        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                element = await self.methods.find(selector).get_element(timeout=0.5)
                if element.is_displayed():
                    # 需要导入ElementBuilder，使用延迟导入避免循环依赖
                    from .element import ElementBuilder
                    return ElementBuilder(self.methods, selector)
            except Exception:
                pass
            await asyncio.sleep(0.1)

        raise Exception(f"Element {selector} was not visible after {timeout} seconds")

    async def for_element_visible(self, selector: str, timeout: Optional[float] = None):
        """等待元素可见"""
        return await self.for_element(selector, timeout)

    async def for_element_absent(self, selector: str, timeout: Optional[float] = None):
        """等待元素消失"""
        if timeout is None:
            timeout = settings.LARGE_TIMEOUT

        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                await self.methods.find(selector).get_element(timeout=0.5)
                await asyncio.sleep(0.1)
            except Exception:
                return self.methods  # 元素不存在，等待成功

        raise Exception(f"Element {selector} was still present after {timeout} seconds")

    async def for_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None):
        """等待文本出现"""
        if timeout is None:
            timeout = settings.LARGE_TIMEOUT

        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                if selector:
                    # 在指定元素中查找文本
                    element = await self.methods.find(selector).get_element(timeout=0.5)
                    element_text = await element.text_content()
                    if text in element_text:
                        return self.methods
                else:
                    # 在整个页面中查找文本
                    page_text = await self.methods.page.evaluate("document.body.innerText")
                    if text in page_text:
                        return self.methods
            except Exception:
                pass
            await asyncio.sleep(0.1)

        raise Exception(f"Text '{text}' was not found after {timeout} seconds")

    async def assert_element(self, selector: str, timeout: Optional[float] = None):
        """断言元素存在"""
        if timeout is None:
            timeout = settings.SMALL_TIMEOUT

        try:
            await self.methods.find(selector).get_element(timeout=timeout)
            return self.methods
        except Exception:
            raise AssertionError(f"Element {selector} was not found")

    async def assert_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None):
        """断言文本存在"""
        if timeout is None:
            timeout = settings.SMALL_TIMEOUT

        if selector:
            # 在指定元素中查找文本
            element = await self.methods.find(selector).get_element(timeout=timeout)
            element_text = await element.text_content()
            if text not in element_text:
                raise AssertionError(f"Text '{text}' not found in element {selector}")
        else:
            # 在整个页面中查找文本
            page_text = await self.methods.page.evaluate("document.body.innerText")
            if text not in page_text:
                raise AssertionError(f"Text '{text}' not found on page")

        return self.methods

    async def seconds(self, duration: float):
        """等待指定秒数"""
        await asyncio.sleep(duration)
        return self.methods
