"""
等待操作组件
包含各种等待条件和断言验证方法
"""

import asyncio
import time
from contextlib import suppress
from typing import Optional, Any

from ... import settings
from .base_component import BaseComponent


class WaitComponent(BaseComponent):
    """等待操作组件"""
    
    async def wait_for_element_visible(self, selector: str, timeout: Optional[float] = None) -> Any:
        """等待元素可见"""
        if not timeout:
            timeout = settings.LARGE_TIMEOUT
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                element = await self.methods.dom.find_element(selector, timeout=0.5)
                if element.is_displayed():
                    return element
            except Exception:
                pass
            await asyncio.sleep(0.1)
        
        raise Exception(f"Element {selector} was not visible after {timeout} seconds")
    
    async def wait_for_element_absent(self, selector: str, timeout: Optional[float] = None) -> None:
        """等待元素消失"""
        if not timeout:
            timeout = settings.LARGE_TIMEOUT
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                await self.methods.dom.find_element(selector, timeout=0.5)
                await asyncio.sleep(0.1)
            except Exception:
                return  # 元素不存在，等待成功
        
        raise Exception(f"Element {selector} was still present after {timeout} seconds")
    
    async def assert_element(self, selector: str, timeout: Optional[float] = None) -> None:
        """断言元素存在"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        
        try:
            await self.methods.dom.find_element(selector, timeout=timeout)
        except Exception:
            raise AssertionError(f"Element {selector} was not found")
    
    async def assert_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None) -> None:
        """断言文本存在"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        
        if selector:
            # 在指定元素中查找文本
            element = await self.methods.dom.find_element(selector, timeout=timeout)
            element_text = await element.text_content()
            if text not in element_text:
                raise AssertionError(f"Text '{text}' not found in element {selector}")
        else:
            # 在整个页面中查找文本
            page_text = await self.page.evaluate("document.body.innerText")
            if text not in page_text:
                raise AssertionError(f"Text '{text}' not found on page")
    
    async def assert_exact_text(self, text: str, selector: str, timeout: Optional[float] = None) -> None:
        """断言精确文本匹配"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        
        element = await self.methods.dom.find_element(selector, timeout=timeout)
        element_text = await element.text_content()
        if text != element_text.strip():
            raise AssertionError(f"Expected exact text '{text}', but got '{element_text.strip()}'")
    
    async def wait_for_text(self, text: str, selector: Optional[str] = None, timeout: Optional[float] = None) -> None:
        """等待文本出现"""
        if not timeout:
            timeout = settings.LARGE_TIMEOUT
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                await self.assert_text(text, selector, timeout=0.5)
                return
            except (Exception, AssertionError):
                await asyncio.sleep(0.1)
        
        raise Exception(f"Text '{text}' was not found after {timeout} seconds")
    
    async def sleep(self, seconds: float) -> None:
        """等待指定秒数"""
        await asyncio.sleep(seconds)
