"""
SBCDP API v2 - 使用组合模式重新设计
更符合开发习惯，完美的IDE支持
"""

import asyncio
import sys
from contextlib import suppress

from .components.dom_component import DOMComponent
from .components.navigation_component import NavigationComponent
from .components.action_component import ActionComponent
from .components.gui_component import GUIComponent
from .components.page_info_component import PageInfoComponent
from .components.wait_component import WaitComponent


class AsyncCDPMethods:
    """异步CDP方法类 - 使用组合模式"""

    def __init__(self, page, driver):
        self.page = page
        self.driver = driver

        # 组合各个功能组件
        self.dom = DOMComponent(self)
        self.navigation = NavigationComponent(self)
        self.action = ActionComponent(self)
        self.gui = GUIComponent(self)
        self.page_info = PageInfoComponent(self)
        self.wait = WaitComponent(self)

        # 为了保持向后兼容，将常用方法代理到主类
        self._setup_method_proxies()

    def _setup_method_proxies(self):
        """设置方法代理，保持向后兼容"""
        # DOM 方法
        self.find_element = self.dom.find_element
        self.select = self.dom.select
        self.select_all = self.dom.select_all

        # Navigation 方法
        self.get = self.navigation.get
        self.open = self.navigation.open
        self.reload = self.navigation.reload

        # Action 方法
        self.click = self.action.click
        self.type = self.action.type
        self.send_keys = self.action.send_keys

        # Wait 方法
        self.wait_for_element_visible = self.wait.wait_for_element_visible
        self.assert_element = self.wait.assert_element

        # GUI 方法
        self.gui_click_x_y = self.gui.gui_click_x_y

        # Page Info 方法
        self.get_title = self.page_info.get_title
        self.get_current_url = self.page_info.get_current_url

    # 基础工具方法
    async def _slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        if (
            (hasattr(self.driver.config, "demo_mode") and self.driver.config.demo_mode)
            or "--demo" in sys.argv
        ):
            await asyncio.sleep(0.48)
        elif (
            (hasattr(self.driver.config, "slow_mode") and self.driver.config.slow_mode)
            or "--slow" in sys.argv
        ):
            await asyncio.sleep(0.24)

    async def _add_light_pause(self):
        """添加轻微暂停"""
        await asyncio.sleep(0.007)

    def _convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        from ..fixtures import page_utils, js_utils
        if page_utils.is_xpath_selector(selector):
            with suppress(Exception):
                css = js_utils.convert_to_css_selector(selector, "xpath")
                if css:
                    return css
        return selector


class SyncCDPMethods(AsyncCDPMethods):
    """同步CDP方法类 - 包装异步方法"""

    def __init__(self, loop, page, driver):
        self.loop = loop
        self.doing = False
        super().__init__(page, driver)

        # 重新设置同步代理
        self._setup_sync_proxies()

    def _setup_sync_proxies(self):
        """设置同步方法代理"""
        # 重写异步方法为同步版本
        async_methods = [
            'find_element', 'select', 'select_all',
            'get', 'open', 'reload',
            'click', 'type', 'send_keys',
            'wait_for_element_visible', 'assert_element',
            'gui_click_x_y',
            'get_title', 'get_current_url'
        ]

        for method_name in async_methods:
            async_method = getattr(self, method_name)
            setattr(self, method_name, self._make_sync_wrapper(async_method))

    def _make_sync_wrapper(self, async_method):
        """创建同步包装器"""
        def sync_wrapper(*args, **kwargs):
            return self._run_sync(async_method(*args, **kwargs))
        return sync_wrapper

    def _run_sync(self, coro):
        """同步执行协程"""
        if not self.doing:
            try:
                self.doing = True
                return self.loop.run_until_complete(coro)
            finally:
                self.doing = False
        return coro
