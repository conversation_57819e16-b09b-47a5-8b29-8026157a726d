"""
SBCDP 异步动作操作方法类
包含点击、输入、滚动、截图等页面操作方法
"""

import asyncio
import os
import re
from contextlib import suppress

from .. import settings
from ..fixtures import js_utils


class Action:
    """异步动作操作方法类"""

    async def click(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._slow_mode_pause_if_set()
        element = await self.find_element(selector, timeout=timeout)
        await element.scroll_into_view()
        await element.click()
        await self._slow_mode_pause_if_set()
        await self.page.wait()

    async def click_active_element(self):
        await self.page.evaluate("document.activeElement.click()")
        await self._slow_mode_pause_if_set()
        await self.page.wait()

    async def click_if_visible(self, selector):
        if self.is_element_visible(selector):
            with suppress(Exception):
                element = await self.find_element(selector, timeout=0)
                await element.scroll_into_view()
                await element.click()
                await self._slow_mode_pause_if_set()
                await self.page.wait()

    async def click_visible_elements(self, selector, limit=0):
        """Finds all matching page elements and clicks visible ones in order.
        If a click reloads or opens a new page, the clicking will stop.
        If no matching elements appear, an Exception will be raised.
        If "limit" is set and > 0, will only click that many elements.
        Also clicks elements that become visible from previous clicks.
        Works best for actions such as clicking all checkboxes on a page.
        Example: self.click_visible_elements('input[type="checkbox"]')"""
        elements = await self.select_all(selector)
        click_count = 0
        for element in elements:
            if limit and limit > 0 and click_count >= limit:
                return
            try:
                width = 0
                height = 0
                try:
                    position = await element.get_position()
                    width = position.width
                    height = position.height
                except Exception:
                    continue
                if width != 0 or height != 0:
                    await element.scroll_into_view()
                    await element.click()
                    click_count += 1
                    await asyncio.sleep(0.042)
                    await self._slow_mode_pause_if_set()
                    await self.page.wait()
            except Exception:
                break

    async def click_nth_element(self, selector, number):
        elements = await self.select_all(selector)
        if len(elements) < number:
            raise Exception(
                "Not enough matching {%s} elements to "
                "click number %s!" % (selector, number)
            )
        number = number - 1
        if number < 0:
            number = 0
        element = elements[number]
        element.scroll_into_view()
        element.click()

    async def click_nth_visible_element(self, selector, number):
        """Finds all matching page elements and clicks the nth visible one.
        Example: await self.click_nth_visible_element('[type="checkbox"]', 5)
                (Clicks the 5th visible checkbox on the page.)"""
        elements = await self.find_visible_elements(selector)
        if len(elements) < number:
            raise Exception(
                "Not enough matching {%s} elements to "
                "click number %s!" % (selector, number)
            )
        number = number - 1
        if number < 0:
            number = 0
        element = elements[number]
        element.scroll_into_view()
        element.click()

    async def click_link(self, link_text):
        (await self.find_elements_by_text(link_text, "a"))[0].click()

    async def mouse_click(self, selector, timeout=None):
        """(Attempt simulating a mouse click)"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._slow_mode_pause_if_set()
        element = await self.find_element(selector, timeout=timeout)
        await element.scroll_into_view()
        await element.mouse_click()
        await self._slow_mode_pause_if_set()
        await self.page.wait()

    async def nested_click(self, parent_selector, selector):
        """
        Find parent element and click on child element inside it.
        (This can be used to click on elements inside an iframe.)
        """
        element = await self.find_element(parent_selector)
        await (await element.query_selector(selector)).mouse_click()
        await self._slow_mode_pause_if_set()
        await self.page.wait()

    async def select_option_by_text(self, dropdown_selector, option):
        element = await self.find_element(dropdown_selector)
        await element.scroll_into_view()
        options = await element.query_selector_all("option")
        for found_option in options:
            if found_option.text.strip() == option.strip():
                found_option.select_option()
                return
        raise Exception(
            "Unable to find text option {%s} in dropdown {%s}!"
            % (dropdown_selector, option)
        )

    async def flash(
        self,
        selector,  # The CSS Selector to flash
        duration=1,  # (seconds) flash duration
        color="44CC88",  # RGB hex flash color
        pause=0,  # (seconds) If 0, the next action starts during flash
    ):
        """Paint a quickly-vanishing dot over an element."""
        selector = self._convert_to_css_if_xpath(selector)
        element = await self.find_element(selector)
        await element.scroll_into_view()
        x_offset = await self._get_x_scroll_offset()
        y_offset = await self._get_y_scroll_offset()
        await element.flash(duration, color, x_offset, y_offset)
        if pause and isinstance(pause, (int, float)):
            await asyncio.sleep(pause)

    async def highlight(self, selector):
        """Highlight an element with multi-colors."""
        selector = self._convert_to_css_if_xpath(selector)
        element = await self.find_element(selector)
        await element.scroll_into_view()
        x_offset = await self._get_x_scroll_offset()
        y_offset = await self._get_y_scroll_offset()
        await element.flash(0.46, "44CC88", x_offset, y_offset)
        await asyncio.sleep(0.15)
        await element.flash(0.42, "8844CC", x_offset, y_offset)
        await asyncio.sleep(0.15)
        await element.flash(0.38, "CC8844", x_offset, y_offset)
        await asyncio.sleep(0.15)
        await element.flash(0.30, "44CC88", x_offset, y_offset)
        await asyncio.sleep(0.30)

    async def remove_elements(self, selector):
        """Remove all elements on the page that match the selector."""
        css_selector = self._convert_to_css_if_xpath(selector)
        css_selector = re.escape(css_selector)  # Add "\\" to special chars
        css_selector = js_utils.escape_quotes_if_needed(css_selector)
        js_code = (
            """var $elements = document.querySelectorAll('%s');
            var index = 0, length = $elements.length;
            for(; index < length; index++){
            $elements[index].remove();}"""
            % css_selector
        )
        with suppress(Exception):
            await self.page.evaluate(js_code)

    async def send_keys(self, selector, text, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._slow_mode_pause_if_set()
        element = await self.select(selector, timeout=timeout)
        await element.scroll_into_view()
        if text.endswith("\n") or text.endswith("\r"):
            text = text[:-1] + "\r\n"
        await element.send_keys(text)
        await self._slow_mode_pause_if_set()
        await self.page.sleep(0.025)

    async def press_keys(self, selector, text, timeout=None):
        """Similar to send_keys(), but presses keys at human speed."""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._slow_mode_pause_if_set()
        element = await self.select(selector, timeout=timeout)
        await element.scroll_into_view()
        submit = False
        if text.endswith("\n") or text.endswith("\r"):
            submit = True
            text = text[:-1]
        for key in text:
            await element.send_keys(key)
            await asyncio.sleep(0.044)
        if submit:
            await element.send_keys("\r\n")
            await asyncio.sleep(0.044)
        await self._slow_mode_pause_if_set()
        await self.page.sleep(0.025)

    async def type(self, selector, text, timeout=None):
        """Similar to send_keys(), but clears the text field first."""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._slow_mode_pause_if_set()
        element = await self.select(selector, timeout=timeout)
        await element.scroll_into_view()
        with suppress(Exception):
            await element.clear_input()
        if text.endswith("\n") or text.endswith("\r"):
            text = text[:-1] + "\r\n"
        await element.send_keys(text)
        await self._slow_mode_pause_if_set()
        await self.page.sleep(0.025)

    async def set_value(self, selector, text, timeout=None):
        """Similar to send_keys(), but clears the text field first."""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._slow_mode_pause_if_set()
        selector = self._convert_to_css_if_xpath(selector)
        element = await self.select(selector, timeout=timeout)
        await element.scroll_into_view()
        press_enter = False
        if text.endswith("\n"):
            text = text[:-1]
            press_enter = True
        value = js_utils.escape_quotes_if_needed(re.escape(text))
        css_selector = re.escape(selector)
        css_selector = js_utils.escape_quotes_if_needed(css_selector)
        set_value_script = (
            """m_elm = document.querySelector('%s');"""
            """m_elm.value = '%s';""" % (css_selector, value)
        )
        await self.page.evaluate(set_value_script)
        the_type = await self.get_element_attribute(selector, "type")
        if the_type == "range":
            # Some input sliders need a mouse event to trigger listeners.
            with suppress(Exception):
                mouse_move_script = (
                    """m_elm = document.querySelector('%s');"""
                    """m_evt = new Event('mousemove');"""
                    """m_elm.dispatchEvent(m_evt);""" % css_selector
                )
                await self.page.evaluate(mouse_move_script)
        elif press_enter:
            await self._add_light_pause()
            await self.send_keys(selector, "\n")
        await self._slow_mode_pause_if_set()
        await self.page.sleep(0.025)

    async def submit(self, selector):
        submit_script = (
            """elm = document.querySelector('%s');
            const event = new KeyboardEvent("keydown", {
                key: "Enter",
                keyCode: 13,
                code: "Enter",
                which: 13,
                bubbles: true
            });
            elm.dispatchEvent(event);""" % selector
        )
        await self.page.evaluate(submit_script)

    async def scroll_into_view(self, selector):
        await (await self.find_element(selector)).scroll_into_view()
        await self.page.wait()

    async def scroll_to_y(self, y):
        y = int(y)
        js_code = "window.scrollTo(0, %s);" % y
        with suppress(Exception):
            await self.page.evaluate(js_code)
            await self.page.wait()

    async def scroll_to_top(self):
        js_code = "window.scrollTo(0, 0);"
        with suppress(Exception):
            await self.page.evaluate(js_code)
            await self.page.wait()

    async def scroll_to_bottom(self):
        js_code = "window.scrollTo(0, 10000);"
        with suppress(Exception):
            await self.page.evaluate(js_code)
            await self.page.wait()

    async def scroll_up(self, amount=25):
        await self.page.scroll_up(amount)
        await self.page.wait()

    async def scroll_down(self, amount=25):
        await self.page.scroll_down(amount)
        await self.page.wait()

    async def save_screenshot(self, name, folder=None, selector=None):
        filename = name
        if folder:
            filename = os.path.join(folder, name)
        if not selector:
            return await self.page.save_screenshot(filename)
        else:
            return await (await self.select(selector)).save_screenshot(filename)

    async def print_to_pdf(self, name, folder=None):
        filename = name
        if folder:
            filename = os.path.join(folder, name)
        await self.page.print_to_pdf(filename)
