"""
SBCDP API 类型定义
为IDE提供更好的类型提示和跳转支持
"""

from typing import Protocol, Any, Optional, runtime_checkable


@runtime_checkable
class BaseMethodsProtocol(Protocol):
    """基础方法协议，定义所有混入类都可以使用的方法"""
    
    async def _slow_mode_pause_if_set(self) -> None:
        """根据配置添加慢速模式暂停"""
        ...
    
    async def _add_light_pause(self) -> None:
        """添加轻微暂停"""
        ...
    
    def _convert_to_css_if_xpath(self, selector: str) -> str:
        """如果是XPath选择器，尝试转换为CSS选择器"""
        ...
    
    def _add_element_methods(self, element: Any) -> Any:
        """为元素添加额外的方法"""
        ...
    
    async def _get_x_scroll_offset(self) -> float:
        """获取X轴滚动偏移"""
        ...
    
    async def _get_y_scroll_offset(self) -> float:
        """获取Y轴滚动偏移"""
        ...


@runtime_checkable  
class DOMMethodsProtocol(BaseMethodsProtocol, Protocol):
    """DOM操作方法协议"""
    
    async def find_element(self, selector: str, best_match: bool = False, timeout: Optional[float] = None) -> Any:
        """查找元素"""
        ...
    
    async def select(self, selector: str, timeout: Optional[float] = None) -> Any:
        """选择元素"""
        ...


@runtime_checkable
class NavigationMethodsProtocol(BaseMethodsProtocol, Protocol):
    """导航方法协议"""
    
    async def get(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs) -> None:
        """导航到URL"""
        ...


@runtime_checkable
class ActionMethodsProtocol(BaseMethodsProtocol, Protocol):
    """动作方法协议"""
    
    async def click(self, selector: str, timeout: Optional[float] = None) -> None:
        """点击元素"""
        ...


@runtime_checkable
class WaitMethodsProtocol(BaseMethodsProtocol, Protocol):
    """等待方法协议"""
    
    async def wait_for_element_visible(self, selector: str, timeout: Optional[float] = None) -> Any:
        """等待元素可见"""
        ...


@runtime_checkable
class GUIMethodsProtocol(BaseMethodsProtocol, Protocol):
    """GUI方法协议"""
    
    async def gui_click_x_y(self, x: float, y: float, timeframe: Optional[float] = None) -> None:
        """GUI点击坐标"""
        ...


@runtime_checkable
class PageInfoMethodsProtocol(BaseMethodsProtocol, Protocol):
    """页面信息方法协议"""
    
    async def get_title(self) -> str:
        """获取页面标题"""
        ...


# 完整的方法协议
@runtime_checkable
class CDPMethodsProtocol(
    DOMMethodsProtocol,
    NavigationMethodsProtocol,
    ActionMethodsProtocol, 
    WaitMethodsProtocol,
    GUIMethodsProtocol,
    PageInfoMethodsProtocol,
    Protocol
):
    """完整的CDP方法协议"""
    pass
