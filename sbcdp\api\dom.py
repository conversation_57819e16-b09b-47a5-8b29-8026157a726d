"""
SBCDP 异步元素操作方法类
包含元素查找、操作和状态检查相关方法
"""

import time
from contextlib import suppress
from typing import Any, Optional, TYPE_CHECKING

from .. import settings
from ..fixtures import page_utils

class DOM:
    """异步元素操作方法类"""

    async def find_element(self, selector, best_match=False, timeout=None):
        """Similar to select(), but also finds elements by text content.
        When using text-based searches, if best_match=False, then will
        find the first element with the text. If best_match=True, then
        if multiple elements have that text, then will use the element
        with the closest text-length to the text being searched for."""
        if not selector:
            raise ValueError("Selector cannot be empty")

        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        selector = self._convert_to_css_if_xpath(selector)
        early_failure = False
        if ":contains(" in selector:
            selector, _ = page_utils.recalculate_selector(
                selector, by="css selector", xp_ok=True
            )
        failure = False
        try:
            if early_failure:
                raise Exception("Failed!")
            element = await self.page.find(selector, best_match=best_match, timeout=timeout)
        except Exception:
            failure = True
            plural = "s"
            if timeout == 1:
                plural = ""
            message = "\n Element {%s} was not found after %s second%s!" % (
                selector,
                timeout,
                plural,
            )
        if failure:
            raise Exception(message)
        element = self._add_element_methods(element)
        await self._slow_mode_pause_if_set()
        return element

    async def find_element_by_text(self, text, tag_name=None, timeout=None):
        """Returns an element by matching text.
        Optionally, provide a tag_name to narrow down the search to an
        element with the given tag. (Eg: a, button, div, script, span)"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        time_now = time.time()
        await self.assert_text(text, timeout=timeout)
        spent = int(time.time() - time_now)
        remaining = 1 + timeout - spent
        if tag_name:
            await self.assert_element(tag_name, timeout=remaining)
        elements = await self.page.find_elements_by_text(text=text)
        if tag_name:
            tag_name = tag_name.lower().strip()
        for element in elements:
            if element and not tag_name:
                element = self._add_element_methods(element)
                return self._add_element_methods(element)
            elif (
                element
                and tag_name in element.tag_name.lower()
                and text.strip() in element.text
            ):
                element = self._add_element_methods(element)
                return self._add_element_methods(element)
            elif (
                element
                and element.parent
                and tag_name in element.parent.tag_name.lower()
                and text.strip() in element.parent.text
            ):
                element = self._add_element_methods(element.parent)
                return self._add_element_methods(element)
            elif (
                element
                and element.parent
                and element.parent.parent
                and tag_name in element.parent.parent.tag_name.lower()
                and text.strip() in element.parent.parent.text
            ):
                element = self._add_element_methods(element.parent.parent)
                return self._add_element_methods(element)
        plural = "s"
        if timeout == 1:
            plural = ""
        raise Exception(
            "Text {%s} with tag {%s} was not found after %s second%s!"
            % (text, tag_name, timeout, plural)
        )

    async def find_all(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        selector = self._convert_to_css_if_xpath(selector)
        elements = await self.page.find_all(selector, timeout=timeout)
        updated_elements = []
        for element in elements:
            element = self._add_element_methods(element)
            updated_elements.append(element)
        return updated_elements

    async def find_elements_by_text(self, text, tag_name=None):
        """Returns a list of elements by matching text.
        Optionally, provide a tag_name to narrow down the search to only
        elements with the given tag. (Eg: a, button, div, script, span)"""
        await self._add_light_pause()
        elements = await self.page.find_elements_by_text(text=text)
        updated_elements = []
        if tag_name:
            tag_name = tag_name.lower().strip()
        for element in elements:
            if element and not tag_name:
                element = self._add_element_methods(element)
                if element not in updated_elements:
                    updated_elements.append(element)
            elif (
                element
                and tag_name in element.tag_name.lower()
                and text.strip() in element.text
            ):
                element = self._add_element_methods(element)
                if element not in updated_elements:
                    updated_elements.append(element)
            elif (
                element
                and element.parent
                and tag_name in element.parent.tag_name.lower()
                and text.strip() in element.parent.text
            ):
                element = self._add_element_methods(element.parent)
                if element not in updated_elements:
                    updated_elements.append(element)
            elif (
                element
                and element.parent
                and element.parent.parent
                and tag_name in element.parent.parent.tag_name.lower()
                and text.strip() in element.parent.parent.text
            ):
                element = self._add_element_methods(element.parent.parent)
                if element not in updated_elements:
                    updated_elements.append(element)
        return updated_elements

    async def select(self, selector, timeout=None):
        """Similar to find_element()."""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        selector = self._convert_to_css_if_xpath(selector)
        if ":contains(" in selector:
            tag_name = selector.split(":contains(")[0].split(" ")[-1]
            text = selector.split(":contains(")[1].split(")")[0][1:-1]
            with suppress(Exception):
                new_timeout = timeout
                if new_timeout < 1:
                    new_timeout = 1
                await self.page.select(tag_name, timeout=new_timeout)
                await self.page.find(text, timeout=new_timeout)
            elements = await self.find_elements_by_text(text, tag_name=tag_name)
            if not elements:
                plural = "s"
                if timeout == 1:
                    plural = ""
                msg = "\n Element {%s} was not found after %s second%s!"
                message = msg % (selector, timeout, plural)
                raise Exception(message)
            element = self._add_element_methods(elements[0])
            return element
        failure = False
        try:
            element = await self.page.select(selector, timeout=timeout)
        except Exception:
            failure = True
            plural = "s"
            if timeout == 1:
                plural = ""
            msg = "\n Element {%s} was not found after %s second%s!"
            message = msg % (selector, timeout, plural)
        if failure:
            raise Exception(message)
        element = self._add_element_methods(element)
        await self._slow_mode_pause_if_set()
        return element

    async def select_all(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._add_light_pause()
        selector = self._convert_to_css_if_xpath(selector)
        elements = await self.page.select_all(selector, timeout=timeout)
        updated_elements = []
        for element in elements:
            element = self._add_element_methods(element)
            updated_elements.append(element)
        return updated_elements

    async def find_elements(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        return await self.select_all(selector, timeout=timeout)

    async def find_visible_elements(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        visible_elements = []
        elements = await self.select_all(selector, timeout=timeout)
        for element in elements:
            with suppress(Exception):
                position = element.get_position()
                if (position.width != 0 or position.height != 0):
                    visible_elements.append(element)
        return visible_elements

    async def get_parent(self, element):
        if isinstance(element, str):
            element = await self.select(element)
        return self._add_element_methods(element.parent)

    async def get_nested_element(self, parent_selector, selector):
        """(Can be used to find an element inside an iframe)"""
        element = await self.find_element(parent_selector)
        return await element.query_selector(selector)

    async def is_element_present(self, selector):
        try:
            await self.select(selector, timeout=0.01)
            return True
        except Exception:
            return False

    async def is_element_visible(self, selector):
        selector = self._convert_to_css_if_xpath(selector)
        element = None
        if ":contains(" not in selector:
            try:
                element = await self.select(selector, timeout=0.01)
            except Exception:
                return False
            if not element:
                return False
            try:
                position = await element.get_position()
                return position.width != 0 or position.height != 0
            except Exception:
                return False
        else:
            with suppress(Exception):
                tag_name = selector.split(":contains(")[0].split(" ")[-1]
                text = selector.split(":contains(")[1].split(")")[0][1:-1]
                await self.page.select(tag_name, timeout=0.1)
                await self.page.find(text, timeout=0.1)
                return True
            return False

    async def is_text_visible(self, text, selector="body"):
        selector = self._convert_to_css_if_xpath(selector)
        text = text.strip()
        element = None
        try:
            element = await self.find_element(selector, timeout=0.1)
        except Exception:
            return False
        with suppress(Exception):
            if text in await element.text_all:
                return True
        return False

    async def is_exact_text_visible(self, text, selector="body"):
        selector = self._convert_to_css_if_xpath(selector)
        text = text.strip()
        element = None
        try:
            element = await self.find_element(selector, timeout=0.1)
        except Exception:
            return False
        with suppress(Exception):
            if text == element.text_all.strip():
                return True
        return False

    async def is_checked(self, selector):
        """Return True if checkbox (or radio button) is checked."""
        selector = self._convert_to_css_if_xpath(selector)
        await self.find_element(selector, timeout=settings.SMALL_TIMEOUT)
        return bool(await self.get_element_attribute(selector, "checked"))

    async def is_selected(self, selector):
        selector = self._convert_to_css_if_xpath(selector)
        return await self.is_checked(selector)

    async def check_if_unchecked(self, selector):
        selector = self._convert_to_css_if_xpath(selector)
        if not self.is_checked(selector):
            await self.click(selector)

    async def select_if_unselected(self, selector):
        selector = self._convert_to_css_if_xpath(selector)
        await self.check_if_unchecked(selector)

    async def uncheck_if_checked(self, selector):
        selector = self._convert_to_css_if_xpath(selector)
        if self.is_checked(selector):
            await self.click(selector)

    async def unselect_if_selected(self, selector):
        selector = self._convert_to_css_if_xpath(selector)
        await self.uncheck_if_checked(selector)

    async def get_text(self, selector):
        return (await self.find_element(selector)).text_all

    async def remove_element(self, selector):
        await (await self.select(selector)).remove_from_dom()

    async def remove_from_dom(self, selector):
        await (await self.select(selector)).remove_from_dom()

    async def focus(self, selector):
        element = await self.find_element(selector)
        await element.scroll_into_view()
        await element.focus()

    async def highlight_overlay(self, selector):
        await (await self.find_element(selector)).highlight_overlay()
