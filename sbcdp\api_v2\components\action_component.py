"""
动作操作组件
包含点击、输入、滚动、截图等页面操作方法
"""

import asyncio
from contextlib import suppress
from typing import Optional

from ... import settings
from .base_component import BaseComponent


class ActionComponent(BaseComponent):
    """动作操作组件"""
    
    async def click(self, selector: str, timeout: Optional[float] = None) -> None:
        """点击元素"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        await self._slow_mode_pause_if_set()
        element = await self.methods.dom.find_element(selector, timeout=timeout)
        await element.scroll_into_view()
        await element.click()
        await self._slow_mode_pause_if_set()
        await self.page.wait()
    
    async def type(self, selector: str, text: str, timeout: Optional[float] = None) -> None:
        """在元素中输入文本"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element = await self.methods.dom.find_element(selector, timeout=timeout)
        await element.clear()
        await element.send_keys(text)
        await self._slow_mode_pause_if_set()
    
    async def send_keys(self, selector: str, text: str, timeout: Optional[float] = None) -> None:
        """发送按键到元素"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element = await self.methods.dom.find_element(selector, timeout=timeout)
        await element.send_keys(text)
        await self._slow_mode_pause_if_set()
    
    async def clear(self, selector: str, timeout: Optional[float] = None) -> None:
        """清空元素内容"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element = await self.methods.dom.find_element(selector, timeout=timeout)
        await element.clear()
        await self._slow_mode_pause_if_set()
    
    async def scroll_to_top(self) -> None:
        """滚动到页面顶部"""
        await self.page.evaluate("window.scrollTo(0, 0)")
        await self._add_light_pause()
    
    async def scroll_to_bottom(self) -> None:
        """滚动到页面底部"""
        await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await self._add_light_pause()
    
    async def scroll_into_view(self, selector: str, timeout: Optional[float] = None) -> None:
        """滚动元素到可视区域"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element = await self.methods.dom.find_element(selector, timeout=timeout)
        await element.scroll_into_view()
        await self._add_light_pause()
    
    async def save_screenshot(self, filename: str = "screenshot.png") -> str:
        """保存截图"""
        screenshot_data = await self.page.screenshot()
        with open(filename, 'wb') as f:
            f.write(screenshot_data)
        return filename
    
    async def flash(self, selector: str, timeout: Optional[float] = None) -> None:
        """闪烁高亮元素"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element = await self.methods.dom.find_element(selector, timeout=timeout)
        await element.flash()
        await self._slow_mode_pause_if_set()
