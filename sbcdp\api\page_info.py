"""
SBCDP 异步页面信息获取方法类
包含页面信息获取、元素属性获取、存储操作等方法
"""

import re
from typing import Any, Optional, TYPE_CHECKING
from contextlib import suppress
import asyncio

from .. import settings
from ..fixtures import js_utils
from ..fixtures import constants


class PageInfo:
    """异步页面信息获取方法类"""

    async def get_title(self):
        return await self.page.evaluate("document.title")

    async def get_current_url(self):
        return await self.page.evaluate("window.location.href")

    async def get_origin(self):
        return await self.page.evaluate("window.location.origin")

    async def get_page_source(self):
        try:
            source = await self.page.evaluate("document.documentElement.outerHTML")
        except Exception:
            await asyncio.sleep(constants.UC.CDP_MODE_OPEN_WAIT)
            source = await self.page.evaluate("document.documentElement.outerHTML")
        return source

    async def get_user_agent(self):
        return await self.page.evaluate("navigator.userAgent")

    async def get_cookie_string(self):
        return await self.page.evaluate("document.cookie")

    async def get_locale_code(self):
        return await self.page.evaluate("navigator.language || navigator.languages[0]")

    async def get_local_storage_item(self, key):
        js_code = """localStorage.getItem('%s');""" % key
        with suppress(Exception):
            return await self.page.evaluate(js_code)

    async def get_session_storage_item(self, key):
        js_code = """sessionStorage.getItem('%s');""" % key
        with suppress(Exception):
            return await self.page.evaluate(js_code)

    async def get_screen_rect(self):
        return await self.page.js_dumps("window.screen")

    async def get_window_rect(self):
        coordinates = {}
        innerWidth = await self.page.evaluate("window.innerWidth")
        innerHeight = await self.page.evaluate("window.innerHeight")
        outerWidth = await self.page.evaluate("window.outerWidth")
        outerHeight = await self.page.evaluate("window.outerHeight")
        pageXOffset = await self.page.evaluate("window.pageXOffset")
        pageYOffset = await self.page.evaluate("window.pageYOffset")
        scrollX = await self.page.evaluate("window.scrollX")
        scrollY = await self.page.evaluate("window.scrollY")
        screenLeft = await self.page.evaluate("window.screenLeft")
        screenTop = await self.page.evaluate("window.screenTop")
        x = await self.page.evaluate("window.screenX")
        y = await self.page.evaluate("window.screenY")
        coordinates["innerWidth"] = innerWidth
        coordinates["innerHeight"] = innerHeight
        coordinates["outerWidth"] = outerWidth
        coordinates["outerHeight"] = outerHeight
        coordinates["width"] = outerWidth
        coordinates["height"] = outerHeight
        coordinates["pageXOffset"] = pageXOffset if pageXOffset else 0
        coordinates["pageYOffset"] = pageYOffset if pageYOffset else 0
        coordinates["scrollX"] = scrollX if scrollX else 0
        coordinates["scrollY"] = scrollY if scrollY else 0
        coordinates["screenLeft"] = screenLeft if screenLeft else 0
        coordinates["screenTop"] = screenTop if screenTop else 0
        coordinates["x"] = x if x else 0
        coordinates["y"] = y if y else 0
        return coordinates

    async def get_window_size(self):
        coordinates = {}
        outerWidth = await self.page.evaluate("window.outerWidth")
        outerHeight = await self.page.evaluate("window.outerHeight")
        coordinates["width"] = outerWidth
        coordinates["height"] = outerHeight
        return coordinates

    async def get_window_position(self):
        coordinates = {}
        x = await self.page.evaluate("window.screenX")
        y = await self.page.evaluate("window.screenY")
        coordinates["x"] = x if x else 0
        coordinates["y"] = y if y else 0
        return coordinates

    async def get_element_rect(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        selector = self._convert_to_css_if_xpath(selector)
        element = await self.select(selector, timeout=timeout)
        await self._add_light_pause()
        coordinates = None
        if ":contains(" in selector:
            position = await element.get_position()
            x = position.x
            y = position.y
            width = position.width
            height = position.height
            coordinates = {"x": x, "y": y, "width": width, "height": height}
        else:
            coordinates = await self.page.js_dumps(
                """document.querySelector('%s').getBoundingClientRect()"""
                % js_utils.escape_quotes_if_needed(re.escape(selector))
            )
        return coordinates

    async def get_element_size(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element_rect = await self.get_element_rect(selector, timeout=timeout)
        return {"width": element_rect["width"], "height": element_rect["height"]}

    async def get_element_position(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element_rect = await self.get_element_rect(selector, timeout=timeout)
        return {"width": element_rect["width"], "height": element_rect["height"]}

    async def get_gui_element_rect(self, selector, timeout=None):
        """(Coordinates are relative to the screen. Not the window.)"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element_rect = await self.get_element_rect(selector, timeout=timeout)
        e_width = element_rect["width"]
        e_height = element_rect["height"]
        window_rect = await self.get_window_rect()
        w_bottom_y = window_rect["y"] + window_rect["height"]
        viewport_height = window_rect["innerHeight"]
        x = window_rect["x"] + element_rect["x"]
        y = w_bottom_y - viewport_height + element_rect["y"]
        y_scroll_offset = window_rect["pageYOffset"]
        y = y - y_scroll_offset
        x = x + window_rect["scrollX"]
        y = y + window_rect["scrollY"]
        return {"height": e_height, "width": e_width, "x": x, "y": y}

    async def get_gui_element_center(self, selector, timeout=None):
        """(Coordinates are relative to the screen. Not the window.)"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        element_rect = await self.get_gui_element_rect(selector, timeout=timeout)
        e_width = element_rect["width"]
        e_height = element_rect["height"]
        e_x = element_rect["x"]
        e_y = element_rect["y"]
        return (e_x + e_width / 2.0) + 0.5, (e_y + e_height / 2.0) + 0.5

    async def get_document(self):
        return await self.page.get_document()

    async def get_flattened_document(self):
        return await self.page.get_flattened_document()

    async def get_element_attributes(self, selector):
        selector = self._convert_to_css_if_xpath(selector)
        return await self.page.js_dumps(
            """document.querySelector('%s')"""
            % js_utils.escape_quotes_if_needed(re.escape(selector))
        )

    async def get_element_attribute(self, selector, attribute):
        """Find an element and return the value of an attribute.
        Raises an exception if there's no such element or attribute."""
        attributes = await self.get_element_attributes(selector)
        with suppress(Exception):
            return attributes[attribute]
        locate = ' %s="' % attribute
        value = await self.get_attribute(selector, attribute)
        if not value and locate not in attributes:
            raise KeyError(attribute)
        return value

    async def get_attribute(self, selector, attribute):
        """Find an element and return the value of an attribute.
        If the element doesn't exist: Raises an exception.
        If the attribute doesn't exist: Returns None."""
        return await (await self.find_element(selector)).get_attribute(attribute)

    async def get_element_html(self, selector):
        """Find an element and return the outerHTML."""
        selector = self._convert_to_css_if_xpath(selector)
        await self.find_element(selector)
        await self._add_light_pause()
        return await self.page.evaluate(
            """document.querySelector('%s').outerHTML"""
            % js_utils.escape_quotes_if_needed(re.escape(selector))
        )

    async def set_locale(self, locale):
        """(Settings will take effect on the next page load)"""
        await self.page.set_locale(locale)

    async def set_local_storage_item(self, key, value):
        js_code = """localStorage.setItem('%s','%s');""" % (key, value)
        with suppress(Exception):
            await self.page.evaluate(js_code)

    async def set_session_storage_item(self, key, value):
        js_code = """sessionStorage.setItem('%s','%s');""" % (key, value)
        with suppress(Exception):
            await self.page.evaluate(js_code)

    async def set_attributes(self, selector, attribute, value):
        """This method uses JavaScript to set/update a common attribute.
        All matching selectors from querySelectorAll() are used.
        Example => (Make all links on a website redirect to Google):
        self.set_attributes("a", "href", "https://google.com")"""
        attribute = re.escape(attribute)
        attribute = js_utils.escape_quotes_if_needed(attribute)
        value = re.escape(value)
        value = js_utils.escape_quotes_if_needed(value)
        css_selector = self._convert_to_css_if_xpath(selector)
        css_selector = re.escape(css_selector)  # Add "\\" to special chars
        css_selector = js_utils.escape_quotes_if_needed(css_selector)
        js_code = """var $elements = document.querySelectorAll('%s');
                  var index = 0, length = $elements.length;
                  for(; index < length; index++){
                  $elements[index].setAttribute('%s','%s');}""" % (
            css_selector,
            attribute,
            value,
        )
        with suppress(Exception):
            await self.page.evaluate(js_code)

    async def get_active_element(self):
        return await self.page.js_dumps("document.activeElement")

    async def get_active_element_css(self):
        from ..js_code import active_css_js

        js_code = active_css_js.get_active_element_css
        js_code = js_code.replace("return getBestSelector", "getBestSelector")
        return await self.page.evaluate(js_code)

    async def evaluate(self, expression):
        """Run a JavaScript expression and return the result."""
        expression = expression.strip()
        exp_list = expression.split("\n")
        if exp_list and exp_list[-1].strip().startswith("return "):
            expression = (
                "\n".join(exp_list[0:-1]) + "\n"
                + exp_list[-1].strip()[len("return "):]
            ).strip()
        return await self.page.evaluate(expression)

    async def js_dumps(self, obj_name):
        """Similar to evaluate(), but for dictionary results."""
        if obj_name.startswith("return "):
            obj_name = obj_name[len("return "):]
        return await self.page.js_dumps(obj_name)
