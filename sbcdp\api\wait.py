"""
SBCDP 异步等待和断言方法类
包含各种等待条件和断言验证方法
"""

import asyncio
import time
from contextlib import suppress
from typing import Optional, Union, TYPE_CHECKING

from .. import settings
from ..driver import element


class Wait:
    """异步等待和断言方法类"""

    async def sleep(self, seconds):
        await asyncio.sleep(seconds)

    async def wait_for_text(self, text, selector="body", timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (timeout * 1000.0)
        text = text.strip()
        element = None
        try:
            element = await self.find_element(selector, timeout=timeout)
        except Exception:
            raise Exception("Element {%s} not found!" % selector)
        for i in range(int(timeout * 10)):
            with suppress(Exception):
                element = await self.find_element(selector, timeout=0.1)
            if text in element.text_all:
                return True
            now_ms = time.time() * 1000.0
            if now_ms >= stop_ms:
                break
            await asyncio.sleep(0.1)
        raise Exception(
            "Text {%s} not found in {%s}! Actual text: {%s}"
            % (text, selector, element.text_all)
        )

    async def wait_for_text_not_visible(self, text, selector="body", timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        text = text.strip()
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (timeout * 1000.0)
        for i in range(int(timeout * 10)):
            if not self.is_text_visible(text, selector):
                return True
            now_ms = time.time() * 1000.0
            if now_ms >= stop_ms:
                break
            await asyncio.sleep(0.1)
        plural = "s"
        if timeout == 1:
            plural = ""
        raise Exception(
            "Text {%s} in {%s} was still visible after %s second%s!"
            % (text, selector, timeout, plural)
        )

    async def wait_for_element_visible(self, selector, timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        try:
            await self.select(selector, timeout=timeout)
        except Exception:
            raise Exception("Element {%s} was not found!" % selector)
        for i in range(30):
            if self.is_element_visible(selector):
                return await self.select(selector)
            await asyncio.sleep(0.1)
        raise Exception("Element {%s} was not visible!" % selector)

    async def wait_for_element_not_visible(self, selector, timeout=None):
        """Wait for element to not be visible on page. (May still be in DOM)"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (timeout * 1000.0)
        for i in range(int(timeout * 10)):
            if not await self.is_element_present(selector):
                return True
            elif not await self.is_element_visible(selector):
                return True
            now_ms = time.time() * 1000.0
            if now_ms >= stop_ms:
                break
            await asyncio.sleep(0.1)
        plural = "s"
        if timeout == 1:
            plural = ""
        raise Exception(
            "Element {%s} was still visible after %s second%s!"
            % (selector, timeout, plural)
        )

    async def wait_for_element_absent(self, selector, timeout=None):
        """Wait for element to not be present in the DOM."""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (timeout * 1000.0)
        for i in range(int(timeout * 10)):
            if not await self.is_element_present(selector):
                return True
            now_ms = time.time() * 1000.0
            if now_ms >= stop_ms:
                break
            await asyncio.sleep(0.1)
        plural = "s"
        if timeout == 1:
            plural = ""
        raise Exception(
            "Element {%s} was still present after %s second%s!"
            % (selector, timeout, plural)
        )

    async def assert_element(self, selector, timeout=None):
        """Same as assert_element_visible()"""
        await self.assert_element_visible(selector, timeout=timeout)
        return True

    async def assert_element_visible(self, selector, timeout=None):
        """Same as assert_element()"""
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        try:
            await self.select(selector, timeout=timeout)
        except Exception:
            raise Exception("Element {%s} was not found!" % selector)
        for i in range(30):
            if self.is_element_visible(selector):
                return True
            await asyncio.sleep(0.1)
        raise Exception("Element {%s} was not visible!" % selector)

    async def assert_text(self, text, selector="body", timeout=None):
        """Same as wait_for_text()"""
        await self.wait_for_text(text, selector=selector, timeout=timeout)
        return True

    async def assert_exact_text(self, text, selector="body", timeout=None):
        if not timeout:
            timeout = settings.SMALL_TIMEOUT
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (timeout * 1000.0)
        text = text.strip()
        element = None
        try:
            element = await self.select(selector, timeout=timeout)
        except Exception:
            raise Exception("Element {%s} not found!" % selector)
        for i in range(int(timeout * 10)):
            with suppress(Exception):
                element = await self.select(selector, timeout=0.1)
            if (
                await self.is_element_visible(selector)
                and text.strip() == element.text_all.strip()
            ):
                return True
            now_ms = time.time() * 1000.0
            if now_ms >= stop_ms:
                break
            await asyncio.sleep(0.1)
        raise Exception(
            "Expected Text {%s}, is not equal to {%s} in {%s}!"
            % (text, element.text_all, selector)
        )

    async def wait_for(
            self,
            selector: Optional[str] = "",
            text: Optional[str] = "",
            timeout: Optional[Union[int, float]] = 10
    ) -> element.Element:
        return await self.page.wait_for(selector, text, timeout=timeout)

