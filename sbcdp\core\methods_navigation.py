"""
SBCDP 异步导航和窗口管理方法类
包含页面导航、窗口管理、标签页操作等方法
"""

import asyncio

from .. import settings
from ..fixtures import shared_utils
from ..fixtures import constants
from ..driver import cdp_util


class AsyncNavigationMethods:
    """异步导航和窗口管理方法类"""

    async def get(self, url, new_tab=False, new_window=False, **kwargs):
        url = shared_utils.fix_url_as_needed(url)
        await self.page.get(url, new_tab=new_tab, new_window=new_window, **kwargs)
        url_protocol = url.split(":")[0]
        safe_url = True
        if url_protocol not in ["about", "data", "chrome"]:
            safe_url = False
        if not safe_url:
            await asyncio.sleep(constants.UC.CDP_MODE_OPEN_WAIT)
            if shared_utils.is_windows():
                await asyncio.sleep(constants.UC.EXTRA_WINDOWS_WAIT)
        else:
            await asyncio.sleep(0.012)
        await self.__slow_mode_pause_if_set()
        await self.page.wait()

    async def open(self, url, new_tab=False, new_window=False, **kwargs):
        await self.get(url, new_tab=new_tab, new_window=new_window, **kwargs)

    async def reload(self, ignore_cache=True, script_to_evaluate_on_load=None):
        await self.page.reload(
            ignore_cache=ignore_cache,
            script_to_evaluate_on_load=script_to_evaluate_on_load,
        )

    async def refresh(self, *args, **kwargs):
        await self.reload(*args, **kwargs)

    async def go_back(self):
        await self.page.back()

    async def go_forward(self):
        await self.page.forward()

    async def get_navigation_history(self):
        return await self.page.get_navigation_history()

    async def bring_active_window_to_front(self):
        await self.page.bring_to_front()
        await self.__add_light_pause()

    async def maximize(self):
        if (await self.get_window())[1].window_state.value == "maximized":
            return
        elif (await self.get_window())[1].window_state.value == "minimized":
            await self.page.maximize()
            await asyncio.sleep(0.044)
        return await self.page.maximize()

    async def minimize(self):
        if (await self.get_window())[1].window_state.value != "minimized":
            return await self.page.minimize()

    async def medimize(self):
        if (await self.get_window())[1].window_state.value == "minimized":
            await self.page.medimize()
            await asyncio.sleep(0.044)
        return await self.page.medimize()

    async def set_window_rect(self, x, y, width, height):
        if (await self.get_window())[1].window_state.value == "minimized":
            await self.page.set_window_size(
                    left=x, top=y, width=width, height=height
            )
            await asyncio.sleep(0.044)
        return await self.page.set_window_size(
                left=x, top=y, width=width, height=height
        )

    async def reset_window_size(self):
        x = settings.WINDOW_START_X
        y = settings.WINDOW_START_Y
        width = settings.CHROME_START_WIDTH
        height = settings.CHROME_START_HEIGHT
        await self.set_window_rect(x, y, width, height)
        await self.__add_light_pause()

    async def open_new_window(self, url=None, switch_to=True):
        return await self.open_new_tab(url=url, switch_to=switch_to)

    async def switch_to_window(self, window):
        await self.switch_to_tab(window)

    async def switch_to_newest_window(self):
        await self.switch_to_tab(-1)

    async def open_new_tab(self, url=None, switch_to=True):
        if not isinstance(url, str):
            url = "about:blank"
        await self.page.get(url, new_tab=True)
        if switch_to:
            await self.switch_to_newest_tab()

    async def switch_to_tab(self, tab):
        driver = self.driver
        if isinstance(tab, int):
            self.page = driver.tabs[tab]
        elif isinstance(tab, cdp_util.Tab):
            self.page = tab
        else:
            raise Exception("`tab` must be an int or a Tab type!")
        await self.bring_active_window_to_front()

    async def switch_to_newest_tab(self):
        await self.switch_to_tab(-1)

    async def close_active_tab(self):
        """Close the active tab.
        The active tab is the one currently controlled by CDP.
        The active tab MIGHT NOT be the currently visible tab!
        (If a page opens a new tab, the new tab WON'T be active)
        To switch the active tab, call: sb.switch_to_tab(tab)"""
        return await self.page.close()

    async def get_active_tab(self):
        """Return the active tab.
        The active tab is the one currently controlled by CDP.
        The active tab MIGHT NOT be the currently visible tab!
        (If a page opens a new tab, the new tab WON'T be active)
        To switch the active tab, call: sb.switch_to_tab(tab)"""
        return self.page

    async def get_tabs(self):
        return self.driver.tabs

    async def get_window(self):
        return await self.page.get_window()

    async def tile_windows(self, windows=None, max_columns=0):
        """Tile windows and return the grid of tiled windows."""
        return await self.driver.tile_windows(windows, max_columns)

    async def grant_permissions(self, permissions, origin=None):
        """Grant specific permissions to the current window.
        Applies to all origins if no origin is specified."""
        return await self.driver.grant_permissions(permissions, origin)

    async def grant_all_permissions(self):
        """Grant all permissions to the current window for all origins."""
        return await self.driver.grant_all_permissions()

    async def reset_permissions(self):
        """Reset permissions for all origins on the current window."""
        return await self.driver.reset_permissions()

    async def get_all_cookies(self, *args, **kwargs):
        return await self.driver.cookies.get_all(*args, **kwargs)

    async def set_all_cookies(self, *args, **kwargs):
        return await self.driver.cookies.set_all(*args, **kwargs)

    async def save_cookies(self, *args, **kwargs):
        return await self.driver.cookies.save(*args, **kwargs)

    async def load_cookies(self, *args, **kwargs):
        return await self.driver.cookies.load(*args, **kwargs)

    async def clear_cookies(self):
        return await self.driver.cookies.clear()

    async def add_handler(self, event, handler):
        self.page.add_handler(event, handler)

    async def internalize_links(self):
        """All `target="_blank"` links become `target="_self"`.
        This prevents those links from opening in a new tab."""
        await self.set_attributes('[target="_blank"]', "target", "_self")
