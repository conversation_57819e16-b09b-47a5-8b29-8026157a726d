[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = True
disallow_untyped_decorators = False
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True
ignore_missing_imports = True
follow_imports = silent
show_column_numbers = True

[mypy-sbcdp.api.*]
# API模块的特殊配置
ignore_errors = False
follow_imports = normal

[mypy-tests.*]
# 测试文件可以更宽松
ignore_errors = True
