"""
基础组件类
所有功能组件的基类
"""

import asyncio
import sys
from contextlib import suppress


class BaseComponent:
    """基础组件类"""
    
    def __init__(self, methods_instance):
        """
        初始化组件
        
        Args:
            methods_instance: AsyncCDPMethods 实例，提供 page 和 driver 访问
        """
        self.methods = methods_instance
        self.page = methods_instance.page
        self.driver = methods_instance.driver
    
    async def _slow_mode_pause_if_set(self):
        """根据配置添加慢速模式暂停"""
        return await self.methods._slow_mode_pause_if_set()
    
    async def _add_light_pause(self):
        """添加轻微暂停"""
        return await self.methods._add_light_pause()
    
    def _convert_to_css_if_xpath(self, selector):
        """如果是XPath选择器，尝试转换为CSS选择器"""
        return self.methods._convert_to_css_if_xpath(selector)
    
    def _add_element_methods(self, element):
        """为元素添加额外的方法"""
        if hasattr(element, 'text_all'):
            return element
        
        # 添加text_all属性
        async def get_text_all():
            try:
                return await element.text_content()
            except Exception:
                return ""
        
        element.text_all = get_text_all()
        return element
