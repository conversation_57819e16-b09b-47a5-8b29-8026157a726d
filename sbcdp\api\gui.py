"""
SBCDP 异步GUI自动化方法类
包含PyAutoGUI相关的所有GUI操作方法
"""

import asyncio
from typing import Any, Optional, TYPE_CHECKING
import os
import sys
from contextlib import suppress

import fasteners

from ..fixtures import shared_utils
from ..fixtures import constants


class GUI:
    """异步GUI自动化方法类"""

    def __make_sure_pyautogui_lock_is_writable(self):
        with suppress(Exception):
            shared_utils.make_writable(constants.MultiBrowser.PYAUTOGUILOCK)

    def __verify_pyautogui_has_a_headed_browser(self):
        """PyAutoGUI requires a headed browser so that it can
        focus on the correct element when performing actions."""
        if self.driver.config.headless:
            raise Exception(
                "PyAutoGUI can't be used in headless mode!"
            )

    def __install_pyautogui_if_missing(self):
        self.__verify_pyautogui_has_a_headed_browser()
        driver = self.driver
        pip_find_lock = fasteners.InterProcessLock(
            constants.PipInstall.FINDLOCK
        )
        with pip_find_lock:  # Prevent issues with multiple processes
            with suppress(Exception):
                shared_utils.make_writable(constants.PipInstall.FINDLOCK)
            try:
                import pyautogui
                with suppress(Exception):
                    use_pyautogui_ver = constants.PyAutoGUI.VER
                    if pyautogui.__version__ != use_pyautogui_ver:
                        del pyautogui
                        shared_utils.pip_install(
                            "pyautogui", version=use_pyautogui_ver
                        )
                        import pyautogui
            except Exception:
                print("\nPyAutoGUI required! Installing now...")
                shared_utils.pip_install(
                    "pyautogui", version=constants.PyAutoGUI.VER
                )
                try:
                    import pyautogui
                except Exception:
                    if (
                        shared_utils.is_linux()
                        and (not self.driver.config.headed or self.driver.config.xvfb)
                        and not driver.config.headless
                        and (
                            not hasattr(self.driver.config, "_virtual_display")
                            or not self.driver.config._virtual_display
                        )
                    ):
                        from sbvirtualdisplay import Display
                        xvfb_width = 1366
                        xvfb_height = 768
                        if (
                            hasattr(self.driver.config, "_xvfb_width")
                            and self.driver.config._xvfb_width
                            and isinstance(self.driver.config._xvfb_width, int)
                            and hasattr(self.driver.config, "_xvfb_height")
                            and self.driver.config._xvfb_height
                            and isinstance(self.driver.config._xvfb_height, int)
                        ):
                            xvfb_width = self.driver.config._xvfb_width
                            xvfb_height = self.driver.config._xvfb_height
                            if xvfb_width < 1024:
                                xvfb_width = 1024
                            self.driver.config._xvfb_width = xvfb_width
                            if xvfb_height < 768:
                                xvfb_height = 768
                            self.driver.config._xvfb_height = xvfb_height
                        with suppress(Exception):
                            xvfb_display = Display(
                                visible=True,
                                size=(xvfb_width, xvfb_height),
                                backend="xvfb",
                                use_xauth=True,
                            )
                            if "--debug-display" in sys.argv:
                                print(
                                    "Starting VDisplay from sb_cdp: (%s, %s)"
                                    % (xvfb_width, xvfb_height)
                                )
                            xvfb_display.start()

    def __get_configured_pyautogui(self, pyautogui_copy):
        if (
            shared_utils.is_linux()
            and hasattr(pyautogui_copy, "_pyautogui_x11")
            and "DISPLAY" in os.environ.keys()
        ):
            if (
                hasattr(self.driver.config, "_pyautogui_x11_display")
                and self.driver.config._pyautogui_x11_display
                and hasattr(pyautogui_copy._pyautogui_x11, "_display")
                and (
                    self.driver.config._pyautogui_x11_display
                    == pyautogui_copy._pyautogui_x11._display
                )
            ):
                pass
            else:
                import Xlib.display
                pyautogui_copy._pyautogui_x11._display = Xlib.display.Display(os.environ['DISPLAY'])
                self.driver.config._pyautogui_x11_display = pyautogui_copy._pyautogui_x11._display
        return pyautogui_copy

    async def gui_press_key(self, key):
        self.__install_pyautogui_if_missing()
        import pyautogui
        pyautogui = self.__get_configured_pyautogui(pyautogui)
        gui_lock = fasteners.InterProcessLock(
            constants.MultiBrowser.PYAUTOGUILOCK
        )
        with gui_lock:
            self.__make_sure_pyautogui_lock_is_writable()
            pyautogui.press(key)
            await asyncio.sleep(0.044)
        await self._slow_mode_pause_if_set()
        await self.page.sleep(0.025)

    async def gui_press_keys(self, keys):
        self.__install_pyautogui_if_missing()
        import pyautogui
        pyautogui = self.__get_configured_pyautogui(pyautogui)
        gui_lock = fasteners.InterProcessLock(
            constants.MultiBrowser.PYAUTOGUILOCK
        )
        with gui_lock:
            self.__make_sure_pyautogui_lock_is_writable()
            for key in keys:
                pyautogui.press(key)
                await asyncio.sleep(0.044)
        await self._slow_mode_pause_if_set()
        await self.page.sleep(0.025)

    async def gui_write(self, text):
        self.__install_pyautogui_if_missing()
        import pyautogui
        pyautogui = self.__get_configured_pyautogui(pyautogui)
        gui_lock = fasteners.InterProcessLock(
            constants.MultiBrowser.PYAUTOGUILOCK
        )
        with gui_lock:
            self.__make_sure_pyautogui_lock_is_writable()
            pyautogui.write(text)
        await self._slow_mode_pause_if_set()
        await self.page.sleep(0.025)

    async def __gui_click_x_y(self, x, y, timeframe=0.25, uc_lock=False):
        self.__install_pyautogui_if_missing()
        import pyautogui
        pyautogui = self.__get_configured_pyautogui(pyautogui)
        screen_width, screen_height = pyautogui.size()
        if x < 0 or y < 0 or x > screen_width or y > screen_height:
            raise Exception(
                "PyAutoGUI cannot click on point (%s, %s)"
                " outside screen. (Width: %s, Height: %s)"
                % (x, y, screen_width, screen_height)
            )
        if uc_lock:
            gui_lock = fasteners.InterProcessLock(
                constants.MultiBrowser.PYAUTOGUILOCK
            )
            with gui_lock:  # Prevent issues with multiple processes
                self.__make_sure_pyautogui_lock_is_writable()
                pyautogui.moveTo(x, y, timeframe, pyautogui.easeOutQuad)
                if timeframe >= 0.25:
                    await asyncio.sleep(0.056)  # Wait if moving at human-speed
                if "--debug" in sys.argv:
                    print(" <DEBUG> pyautogui.click(%s, %s)" % (x, y))
                pyautogui.click(x=x, y=y)
        else:
            # Called from a method where the gui_lock is already active
            pyautogui.moveTo(x, y, timeframe, pyautogui.easeOutQuad)
            if timeframe >= 0.25:
                await asyncio.sleep(0.056)  # Wait if moving at human-speed
            if "--debug" in sys.argv:
                print(" <DEBUG> pyautogui.click(%s, %s)" % (x, y))
            pyautogui.click(x=x, y=y)

    async def gui_click_x_y(self, x, y, timeframe=0.25):
        gui_lock = fasteners.InterProcessLock(
            constants.MultiBrowser.PYAUTOGUILOCK
        )
        with gui_lock:  # Prevent issues with multiple processes
            self.__make_sure_pyautogui_lock_is_writable()
            self.__install_pyautogui_if_missing()
            import pyautogui
            pyautogui = self.__get_configured_pyautogui(pyautogui)
            width_ratio = 1.0
            if shared_utils.is_windows():
                window_rect = await self.get_window_rect()
                width = window_rect["width"]
                height = window_rect["height"]
                win_x = window_rect["x"]
                win_y = window_rect["y"]
                scr_width = pyautogui.size().width
                await self.maximize()
                await self._add_light_pause()
                win_width = (await self.get_window_size())["width"]
                width_ratio = round(float(scr_width) / float(win_width), 2)
                width_ratio += 0.01
                if width_ratio < 0.45 or width_ratio > 2.55:
                    width_ratio = 1.01
                self.driver.config._saved_width_ratio = width_ratio
                await self.minimize()
                await self._add_light_pause()
                await self.set_window_rect(win_x, win_y, width, height)
                await self._add_light_pause()
                x = x * width_ratio
                y = y * width_ratio
            await self.bring_active_window_to_front()
            await self.__gui_click_x_y(x, y, timeframe=timeframe, uc_lock=False)

    async def gui_click_element(self, selector, timeframe=0.25):
        await self._slow_mode_pause_if_set()
        x, y = await self.get_gui_element_center(selector)
        await self._add_light_pause()
        await self.gui_click_x_y(x, y, timeframe=timeframe)
        await self._slow_mode_pause_if_set()
        await self.page.wait()

    async def __gui_drag_drop(self, x1, y1, x2, y2, timeframe=0.25, uc_lock=False):
        self.__install_pyautogui_if_missing()
        import pyautogui
        pyautogui = self.__get_configured_pyautogui(pyautogui)
        screen_width, screen_height = pyautogui.size()
        if x1 < 0 or y1 < 0 or x1 > screen_width or y1 > screen_height:
            raise Exception(
                "PyAutoGUI cannot drag-drop from point (%s, %s)"
                " outside screen. (Width: %s, Height: %s)"
                % (x1, y1, screen_width, screen_height)
            )
        if x2 < 0 or y2 < 0 or x2 > screen_width or y2 > screen_height:
            raise Exception(
                "PyAutoGUI cannot drag-drop to point (%s, %s)"
                " outside screen. (Width: %s, Height: %s)"
                % (x2, y2, screen_width, screen_height)
            )
        if uc_lock:
            gui_lock = fasteners.InterProcessLock(
                constants.MultiBrowser.PYAUTOGUILOCK
            )
            with gui_lock:  # Prevent issues with multiple processes
                pyautogui.moveTo(x1, y1, 0.25, pyautogui.easeOutQuad)
                await self._add_light_pause()
                if "--debug" in sys.argv:
                    print(" <DEBUG> pyautogui.moveTo(%s, %s)" % (x1, y1))
                pyautogui.dragTo(x2, y2, button="left", duration=timeframe)
        else:
            # Called from a method where the gui_lock is already active
            pyautogui.moveTo(x1, y1, 0.25, pyautogui.easeOutQuad)
            await self._add_light_pause()
            if "--debug" in sys.argv:
                print(" <DEBUG> pyautogui.dragTo(%s, %s)" % (x2, y2))
            pyautogui.dragTo(x2, y2, button="left", duration=timeframe)

    async def gui_drag_drop_points(self, x1, y1, x2, y2, timeframe=0.35):
        """Use PyAutoGUI to drag-and-drop from one point to another.
        Can simulate click-and-hold when using the same point twice."""
        gui_lock = fasteners.InterProcessLock(
            constants.MultiBrowser.PYAUTOGUILOCK
        )
        with gui_lock:  # Prevent issues with multiple processes
            self.__install_pyautogui_if_missing()
            import pyautogui
            pyautogui = self.__get_configured_pyautogui(pyautogui)
            width_ratio = 1.0
            if shared_utils.is_windows():
                window_rect = await self.get_window_rect()
                width = window_rect["width"]
                height = window_rect["height"]
                win_x = window_rect["x"]
                win_y = window_rect["y"]
                scr_width = pyautogui.size().width
                await self.maximize()
                await self._add_light_pause()
                win_width = (await self.get_window_size())["width"]
                width_ratio = round(float(scr_width) / float(win_width), 2)
                width_ratio += 0.01
                if width_ratio < 0.45 or width_ratio > 2.55:
                    width_ratio = 1.01
                self.driver.config._saved_width_ratio = width_ratio
                await self.minimize()
                await self._add_light_pause()
                await self.set_window_rect(win_x, win_y, width, height)
                await self._add_light_pause()
                x1 = x1 * width_ratio
                y1 = y1 * width_ratio
                x2 = x2 * width_ratio
                y2 = y2 * width_ratio
            await self.bring_active_window_to_front()
            await self.__gui_drag_drop(
                x1, y1, x2, y2, timeframe=timeframe, uc_lock=False
            )
        await self._slow_mode_pause_if_set()
        await self.page.wait()

    async def gui_drag_and_drop(self, drag_selector, drop_selector, timeframe=0.35):
        """Use PyAutoGUI to drag-and-drop from one selector to another.
        Can simulate click-and-hold when using the same selector twice."""
        await self._slow_mode_pause_if_set()
        await self.bring_active_window_to_front()
        x1, y1 = await self.get_gui_element_center(drag_selector)
        await self._add_light_pause()
        x2, y2 = await self.get_gui_element_center(drop_selector)
        await self._add_light_pause()
        await self.gui_drag_drop_points(x1, y1, x2, y2, timeframe=timeframe)

    async def gui_click_and_hold(self, selector, timeframe=0.35):
        """Use PyAutoGUI to click-and-hold a selector."""
        await self._slow_mode_pause_if_set()
        await self.bring_active_window_to_front()
        x, y = await self.get_gui_element_center(selector)
        await self._add_light_pause()
        await self.gui_drag_drop_points(x, y, x, y, timeframe=timeframe)

    async def __gui_hover_x_y(self, x, y, timeframe=0.25, uc_lock=False):
        self.__install_pyautogui_if_missing()
        import pyautogui
        pyautogui = self.__get_configured_pyautogui(pyautogui)
        screen_width, screen_height = pyautogui.size()
        if x < 0 or y < 0 or x > screen_width or y > screen_height:
            raise Exception(
                "PyAutoGUI cannot hover on point (%s, %s)"
                " outside screen. (Width: %s, Height: %s)"
                % (x, y, screen_width, screen_height)
            )
        if uc_lock:
            gui_lock = fasteners.InterProcessLock(
                constants.MultiBrowser.PYAUTOGUILOCK
            )
            with gui_lock:  # Prevent issues with multiple processes
                pyautogui.moveTo(x, y, timeframe, pyautogui.easeOutQuad)
                await asyncio.sleep(0.056)
                if "--debug" in sys.argv:
                    print(" <DEBUG> pyautogui.moveTo(%s, %s)" % (x, y))
        else:
            # Called from a method where the gui_lock is already active
            pyautogui.moveTo(x, y, timeframe, pyautogui.easeOutQuad)
            await asyncio.sleep(0.056)
            if "--debug" in sys.argv:
                print(" <DEBUG> pyautogui.moveTo(%s, %s)" % (x, y))

    async def gui_hover_x_y(self, x, y, timeframe=0.25):
        gui_lock = fasteners.InterProcessLock(
            constants.MultiBrowser.PYAUTOGUILOCK
        )
        with gui_lock:  # Prevent issues with multiple processes
            self.__install_pyautogui_if_missing()
            import pyautogui
            pyautogui = self.__get_configured_pyautogui(pyautogui)
            width_ratio = 1.0
            if (
                shared_utils.is_windows()
                and (
                    not hasattr(self.driver.config, "_saved_width_ratio")
                    or not self.driver.config._saved_width_ratio
                )
            ):
                window_rect = await self.get_window_rect()
                width = window_rect["width"]
                height = window_rect["height"]
                win_x = window_rect["x"]
                win_y = window_rect["y"]
                if (
                    hasattr(self.driver.config, "_saved_width_ratio")
                    and self.driver.config._saved_width_ratio
                ):
                    width_ratio = self.driver.config._saved_width_ratio
                else:
                    scr_width = pyautogui.size().width
                    await self.maximize()
                    await self._add_light_pause()
                    win_width = (await self.get_window_size())["width"]
                    width_ratio = round(float(scr_width) / float(win_width), 2)
                    width_ratio += 0.01
                    if width_ratio < 0.45 or width_ratio > 2.55:
                        width_ratio = 1.01
                    self.driver.config._saved_width_ratio = width_ratio
                await self.set_window_rect(win_x, win_y, width, height)
                await self._add_light_pause()
                await self.bring_active_window_to_front()
            elif (
                shared_utils.is_windows()
                and hasattr(self.driver.config, "_saved_width_ratio")
                and self.driver.config._saved_width_ratio
            ):
                width_ratio = self.driver.config._saved_width_ratio
                await self.bring_active_window_to_front()
            if shared_utils.is_windows():
                x = x * width_ratio
                y = y * width_ratio
                await self.__gui_hover_x_y(x, y, timeframe=timeframe, uc_lock=False)
                return
            await self.bring_active_window_to_front()
            await self.__gui_hover_x_y(x, y, timeframe=timeframe, uc_lock=False)

    async def gui_hover_element(self, selector, timeframe=0.25):
        await self._slow_mode_pause_if_set()
        element_rect = await self.get_gui_element_rect(selector)
        width = element_rect["width"]
        height = element_rect["height"]
        if width > 0 and height > 0:
            x, y = await self.get_gui_element_center(selector)
            await self.bring_active_window_to_front()
            await self.__gui_hover_x_y(x, y, timeframe=timeframe)
            await self._slow_mode_pause_if_set()
        await self.page.wait()

    async def gui_hover_and_click(self, hover_selector, click_selector):
        gui_lock = fasteners.InterProcessLock(
            constants.MultiBrowser.PYAUTOGUILOCK
        )
        with gui_lock:
            self.__make_sure_pyautogui_lock_is_writable()
            await self.bring_active_window_to_front()
            await self.gui_hover_element(hover_selector)
            await asyncio.sleep(0.15)
            await self.gui_hover_element(click_selector)
            await self.click(click_selector)
