"""
SBCDP API v3 元素构建器
处理元素查找和操作相关功能
"""

from contextlib import suppress
from typing import Optional

from ... import settings


class ElementBuilder:
    """元素操作构建器"""

    def __init__(self, methods, selector: str):
        self.methods = methods
        self.selector = selector
        self._element = None
        self._timeout = None

    async def _get_element(self, timeout: Optional[float] = None):
        """获取元素（懒加载）"""
        if timeout is None:
            timeout = self._timeout or settings.SMALL_TIMEOUT

        await self.methods._add_light_pause()
        selector = self.methods._convert_to_css_if_xpath(self.selector)

        try:
            element = await self.methods.page.find(selector, timeout=timeout)
            element = self.methods._add_element_methods(element)
            await self.methods._slow_mode_pause_if_set()
            return element
        except Exception:
            plural = "s" if timeout != 1 else ""
            message = f"\n Element {{{self.selector}}} was not found after {timeout} second{plural}!"
            raise Exception(message)

    async def get_element(self, timeout: Optional[float] = None):
        """获取元素（公共方法）"""
        return await self._get_element(timeout)

    async def get_all_elements(self, timeout: Optional[float] = None):
        """获取所有匹配的元素"""
        if timeout is None:
            timeout = settings.SMALL_TIMEOUT

        await self.methods._add_light_pause()
        selector = self.methods._convert_to_css_if_xpath(self.selector)

        elements = await self.methods.page.find_elements(selector, timeout=timeout)
        updated_elements = []
        for element in elements:
            element = self.methods._add_element_methods(element)
            updated_elements.append(element)
        await self.methods._slow_mode_pause_if_set()
        return updated_elements

    async def click(self, timeout: Optional[float] = None):
        """点击元素"""
        element = await self._get_element(timeout)
        await element.scroll_into_view_async()
        await element.click_async()
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.wait()
        return self.methods

    async def type(self, text: str, timeout: Optional[float] = None):
        """输入文本"""
        element = await self._get_element(timeout)
        await element.scroll_into_view_async()

        # 清空输入框
        with suppress(Exception):
            await element.clear_input_async()

        # 处理换行符
        if text.endswith("\n") or text.endswith("\r"):
            text = text[:-1] + "\r\n"

        # 发送按键
        await element.send_keys_async(text)
        await self.methods._slow_mode_pause_if_set()
        await self.methods.page.sleep(0.025)
        return self.methods

    async def send_keys(self, text: str, timeout: Optional[float] = None):
        """发送按键"""
        element = await self._get_element(timeout)
        await element.send_keys_async(text)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def clear(self, timeout: Optional[float] = None):
        """清空元素"""
        element = await self._get_element(timeout)
        await element.clear_input_async()
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def set_value(self, value: str, timeout: Optional[float] = None):
        """设置值"""
        element = await self._get_element(timeout)
        await element.set_value_async(value)
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    async def get_text(self, timeout: Optional[float] = None) -> str:
        """获取文本"""
        element = await self._get_element(timeout)
        try:
            # 直接使用元素的text属性
            return element.text or ""
        except Exception:
            return ""

    async def get_attribute(self, name: str, timeout: Optional[float] = None) -> str:
        """获取属性"""
        element = await self._get_element(timeout)
        # 特殊处理value属性
        if name.lower() == "value":
            # 对于输入框，使用JavaScript获取实际的DOM值
            try:
                js_code = f"document.querySelector('{self.selector}').value"
                value = await self.methods.page.evaluate(js_code)
                return value or ""
            except Exception:
                return ""

        # 从元素的attrs字典中获取其他属性
        try:
            return element.attrs.get(name, "")
        except Exception:
            return ""

    async def is_visible(self, timeout: Optional[float] = None) -> bool:
        """检查是否可见"""
        try:
            element = await self._get_element(timeout or 0.1)
            return element.is_displayed()
        except Exception:
            return False

    async def is_present(self, timeout: Optional[float] = None) -> bool:
        """检查是否存在"""
        try:
            await self._get_element(timeout or 0.1)
            return True
        except Exception:
            return False

    async def scroll_into_view(self, timeout: Optional[float] = None):
        """滚动到可视区域"""
        element = await self._get_element(timeout)
        await element.scroll_into_view_async()
        await self.methods._add_light_pause()
        return self.methods

    async def flash(self, timeout: Optional[float] = None):
        """闪烁高亮"""
        element = await self._get_element(timeout)
        await element.flash_async()
        await self.methods._slow_mode_pause_if_set()
        return self.methods

    def and_then(self):
        """链式操作连接符"""
        return self.methods
