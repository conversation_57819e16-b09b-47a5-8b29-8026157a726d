"""
SBCDP API v3 使用示例
展示新的模块化架构的使用方法
"""

import asyncio
from sbcdp import Chrome
from sbcdp.api_v3 import AsyncCDPMethods


async def basic_example():
    """基础使用示例"""
    async with Chrome() as chrome:
        methods = AsyncCDPMethods(chrome.page, chrome.driver)
        
        # 建造者模式 - 流式调用
        await (methods
               .navigate().get("https://example.com")
               .find("#search").type("hello world")
               .find("#submit").click()
               .wait().for_text("Results"))
        
        # 获取页面信息
        title = await methods.page_info().title()
        url = await methods.page_info().url()
        
        print(f"页面标题: {title}")
        print(f"当前URL: {url}")


async def element_operations_example():
    """元素操作示例"""
    async with Chrome() as chrome:
        methods = AsyncCDPMethods(chrome.page, chrome.driver)
        
        # 导航到测试页面
        await methods.navigate().to("data:text/html,<html><body><input id='test' placeholder='输入文本'><button id='btn'>点击</button></body></html>")
        
        # 元素操作
        input_element = methods.find("#test")
        await input_element.type("测试文本")
        
        # 获取输入的值
        value = await input_element.get_attribute("value")
        print(f"输入的值: {value}")
        
        # 点击按钮
        await methods.find("#btn").click()


async def wait_operations_example():
    """等待操作示例"""
    async with Chrome() as chrome:
        methods = AsyncCDPMethods(chrome.page, chrome.driver)
        
        # 导航到页面
        await methods.navigate().to("https://example.com")
        
        # 等待元素出现
        await methods.wait().for_element("h1", timeout=10)
        
        # 断言元素存在
        await methods.wait().assert_element("h1")
        
        # 等待文本出现
        await methods.wait().for_text("Example", timeout=5)


async def page_info_example():
    """页面信息示例"""
    async with Chrome() as chrome:
        methods = AsyncCDPMethods(chrome.page, chrome.driver)
        
        await methods.navigate().to("https://example.com")
        
        # 获取页面信息
        page_info = methods.page_info()
        
        title = await page_info.title()
        url = await page_info.url()
        source = await page_info.source()
        
        print(f"标题: {title}")
        print(f"URL: {url}")
        print(f"源码长度: {len(source)}")
        
        # 执行JavaScript
        result = await page_info.evaluate("document.body.children.length")
        print(f"Body子元素数量: {result}")
        
        # 保存截图
        filename = await page_info.save_screenshot("example.png")
        print(f"截图保存为: {filename}")


async def backward_compatibility_example():
    """向后兼容示例"""
    async with Chrome() as chrome:
        methods = AsyncCDPMethods(chrome.page, chrome.driver)
        
        # 使用原有的直接调用方式
        await methods.get("https://example.com")
        await methods.type("#search", "hello")
        await methods.click("#submit")
        
        title = await methods.get_title()
        text = await methods.get_text("h1")
        
        print(f"标题: {title}")
        print(f"H1文本: {text}")


async def mixed_style_example():
    """混合风格示例"""
    async with Chrome() as chrome:
        methods = AsyncCDPMethods(chrome.page, chrome.driver)
        
        # 混合使用建造者模式和直接调用
        await methods.navigate().to("https://example.com")
        
        # 使用建造者模式进行复杂操作
        search_element = methods.find("#search")
        await search_element.type("Python automation")
        
        # 使用直接调用进行简单操作
        await methods.click("#submit")
        
        # 使用建造者模式进行等待
        await methods.wait().for_text("Results", timeout=10)
        
        # 使用直接调用获取结果
        results = await methods.get_text(".results")
        print(f"搜索结果: {results}")


if __name__ == "__main__":
    print("SBCDP API v3 使用示例")
    print("=" * 50)
    
    # 运行示例（注释掉实际执行，仅作为文档）
    asyncio.run(basic_example())
    # asyncio.run(element_operations_example())
    # asyncio.run(wait_operations_example())
    # asyncio.run(page_info_example())
    # asyncio.run(backward_compatibility_example())
    # asyncio.run(mixed_style_example())
    
    print("所有示例代码已准备就绪！")
    print("取消注释相应的行来运行特定示例。")
