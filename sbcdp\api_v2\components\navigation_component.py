"""
导航操作组件
包含页面导航、窗口管理、标签页操作等方法
"""

import asyncio
from typing import Optional

from ...fixtures import shared_utils, constants
from .base_component import BaseComponent


class NavigationComponent(BaseComponent):
    """导航操作组件"""
    
    async def get(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs) -> None:
        """导航到指定URL"""
        url = shared_utils.fix_url_as_needed(url)
        await self.page.get(url, new_tab=new_tab, new_window=new_window, **kwargs)
        url_protocol = url.split(":")[0]
        safe_url = True
        if url_protocol not in ["about", "data", "chrome"]:
            safe_url = False
        if not safe_url:
            await asyncio.sleep(constants.UC.CDP_MODE_OPEN_WAIT)
            if shared_utils.is_windows():
                await asyncio.sleep(constants.UC.EXTRA_WINDOWS_WAIT)
        else:
            await asyncio.sleep(0.012)
        await self._slow_mode_pause_if_set()
        await self.page.wait()
    
    async def open(self, url: str, new_tab: bool = False, new_window: bool = False, **kwargs) -> None:
        """打开URL（get方法的别名）"""
        await self.get(url, new_tab=new_tab, new_window=new_window, **kwargs)
    
    async def reload(self, ignore_cache: bool = True, script_to_evaluate_on_load: Optional[str] = None) -> None:
        """重新加载页面"""
        await self.page.reload(ignore_cache=ignore_cache, script_to_evaluate_on_load=script_to_evaluate_on_load)
        await self._slow_mode_pause_if_set()
        await self.page.wait()
    
    async def go_back(self) -> None:
        """后退"""
        await self.page.go_back()
        await self._slow_mode_pause_if_set()
        await self.page.wait()
    
    async def go_forward(self) -> None:
        """前进"""
        await self.page.go_forward()
        await self._slow_mode_pause_if_set()
        await self.page.wait()
    
    async def switch_to_tab(self, tab_number: int) -> None:
        """切换到指定标签页"""
        await self.page.switch_to_tab(tab_number)
        await self._slow_mode_pause_if_set()
    
    async def open_new_tab(self, url: Optional[str] = None) -> None:
        """打开新标签页"""
        if url:
            await self.get(url, new_tab=True)
        else:
            await self.page.open_new_tab()
        await self._slow_mode_pause_if_set()
    
    async def close_active_tab(self) -> None:
        """关闭当前标签页"""
        await self.page.close_active_tab()
        await self._slow_mode_pause_if_set()
