[build-system]
requires = ["setuptools>=70.2.0", "wheel>=0.44.0"]
build-backend = "setuptools.build_meta"

[project]
name = "sbcdp"
readme = "README.md"
dynamic = [
    "version",
    "license",
    "authors",
    "scripts",
    "keywords",
    "classifiers",
    "description",
    "entry-points",
    "dependencies",
    "requires-python",
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
]

[project.urls]
"Homepage" = "https://github.com/ConlinH/sbcdp"
"Source" = "https://github.com/ConlinH/sbcdp"
"Repository" = "https://github.com/ConlinH/sbcdp"
"Bug Tracker" = "https://github.com/ConlinH/sbcdp/issues"
"Documentation" = "https://github.com/ConlinH/sbcdp#readme"

[tool.setuptools]
packages = [
    "sbcdp",
    "sbcdp.core",
    "sbcdp.driver",
    "sbcdp.fixtures",
]

[tool.setuptools.package-data]
sbcdp = ["*.txt", "*.md"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
]
asyncio_mode = "auto"
markers = [
    "asyncio: mark test as async",
    "slow: mark test as slow running",
    "integration: mark test as integration test",
]
